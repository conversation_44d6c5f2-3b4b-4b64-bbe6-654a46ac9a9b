/**
 * Authentication functionality
 */

/**
 * Load job search form
 */
function loadJobSearchForm() {
    const jobSearchView = document.getElementById('job-search-view');
    jobSearchView.innerHTML = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Job Search</h1>
        </div>

        <div class="search-form">
            <form id="job-search-form" onsubmit="startJobSearch(event)">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="job-keywords" class="form-label">Keywords *</label>
                        <input type="text" class="form-control" id="job-keywords" placeholder="e.g., Python Developer, Software Engineer" required>
                        <div class="form-text">Enter job titles, skills, or keywords</div>
                    </div>
                    <div class="col-md-6">
                        <label for="job-location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="job-location" placeholder="e.g., New York, NY or Remote">
                        <div class="form-text">City, state, or "Remote"</div>
                    </div>
                    <div class="col-md-4">
                        <label for="job-date-posted" class="form-label">Date Posted</label>
                        <select class="form-select" id="job-date-posted">
                            <option value="past_24_hours">Past 24 hours</option>
                            <option value="past_week" selected>Past week</option>
                            <option value="past_month">Past month</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="job-type" class="form-label">Job Type</label>
                        <select class="form-select" id="job-type">
                            <option value="">Any</option>
                            <option value="full-time">Full-time</option>
                            <option value="part-time">Part-time</option>
                            <option value="contract">Contract</option>
                            <option value="internship">Internship</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="job-experience" class="form-label">Experience Level</label>
                        <select class="form-select" id="job-experience">
                            <option value="">Any</option>
                            <option value="entry">Entry level</option>
                            <option value="mid">Mid level</option>
                            <option value="senior">Senior level</option>
                            <option value="executive">Executive</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="job-salary-min" class="form-label">Minimum Salary</label>
                        <input type="number" class="form-control" id="job-salary-min" placeholder="e.g., 50000">
                        <div class="form-text">Annual salary in USD (optional)</div>
                    </div>
                    <div class="col-md-6">
                        <label for="job-salary-max" class="form-label">Maximum Salary</label>
                        <input type="number" class="form-control" id="job-salary-max" placeholder="e.g., 100000">
                        <div class="form-text">Annual salary in USD (optional)</div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="search-spinner"></span>
                        <i class="fas fa-search me-2"></i>Start Job Search
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="clearSearchForm()">
                        <i class="fas fa-times me-2"></i>Clear
                    </button>
                </div>
            </form>
        </div>

        <!-- Search Status -->
        <div id="search-status" class="mt-4" style="display: none;">
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                    <div>
                        <strong>Searching for jobs...</strong>
                        <div class="small">This may take a few minutes. You can continue using the application while the search runs in the background.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Searches -->
        <div class="mt-5">
            <h4>Recent Searches</h4>
            <div id="recent-searches">
                <div class="text-muted">No recent searches</div>
            </div>
        </div>
    `;
}

/**
 * Start job search
 */
async function startJobSearch(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const spinner = document.getElementById('search-spinner');
    const statusDiv = document.getElementById('search-status');
    
    try {
        // Show loading state
        spinner.classList.remove('d-none');
        submitButton.disabled = true;
        statusDiv.style.display = 'block';
        
        // Collect form data
        const searchData = {
            keywords: document.getElementById('job-keywords').value,
            location: document.getElementById('job-location').value || null,
            date_posted: document.getElementById('job-date-posted').value,
            job_type: document.getElementById('job-type').value || null,
            experience_level: document.getElementById('job-experience').value || null,
            salary_min: parseFloat(document.getElementById('job-salary-min').value) || null,
            salary_max: parseFloat(document.getElementById('job-salary-max').value) || null
        };
        
        // Start job search
        const response = await apiCall('/jobs/search', 'POST', searchData);
        
        if (response) {
            showNotification(response.message, 'success');
            
            // Update status
            statusDiv.innerHTML = `
                <div class="alert alert-success">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3"></i>
                        <div>
                            <strong>Job search started successfully!</strong>
                            <div class="small">Search ID: ${response.search_id}</div>
                            <div class="small">Results will appear in the Jobs section as they are found.</div>
                        </div>
                    </div>
                </div>
            `;
            
            // Redirect to jobs page after a delay
            setTimeout(() => {
                showJobs();
            }, 3000);
        }
        
    } catch (error) {
        showNotification('Failed to start job search: ' + error.message, 'error');
        statusDiv.style.display = 'none';
    } finally {
        spinner.classList.add('d-none');
        submitButton.disabled = false;
    }
}

/**
 * Clear search form
 */
function clearSearchForm() {
    document.getElementById('job-search-form').reset();
}

/**
 * Load settings form
 */
function loadSettingsForm() {
    const settingsView = document.getElementById('settings-view');
    settingsView.innerHTML = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Settings</h1>
        </div>

        <!-- LinkedIn Credentials -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fab fa-linkedin me-2"></i>LinkedIn Credentials
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> Your LinkedIn credentials are encrypted and stored securely. 
                    They are only used for job searching and application automation.
                </div>
                
                <form id="linkedin-credentials-form" onsubmit="saveLinkedInCredentials(event)">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="linkedin-email" class="form-label">LinkedIn Email</label>
                            <input type="email" class="form-control" id="linkedin-email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="linkedin-password" class="form-label">LinkedIn Password</label>
                            <input type="password" class="form-control" id="linkedin-password" required>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary">
                            <span class="spinner-border spinner-border-sm d-none me-2" id="credentials-spinner"></span>
                            <i class="fas fa-save me-2"></i>Save Credentials
                        </button>
                        <button type="button" class="btn btn-outline-danger ms-2" onclick="deleteLinkedInCredentials()">
                            <i class="fas fa-trash me-2"></i>Delete Credentials
                        </button>
                    </div>
                </form>
                
                <div class="mt-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="credentials-status" disabled>
                        <label class="form-check-label" for="credentials-status">
                            LinkedIn credentials are configured
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>Application Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="max-applications" class="form-label">Max Applications per Day</label>
                        <input type="number" class="form-control" id="max-applications" value="10" min="1" max="50">
                        <div class="form-text">Recommended: 5-10 applications per day</div>
                    </div>
                    <div class="col-md-6">
                        <label for="auto-apply" class="form-label">Auto-Apply Mode</label>
                        <select class="form-select" id="auto-apply">
                            <option value="manual">Manual review required</option>
                            <option value="auto">Automatic application</option>
                        </select>
                        <div class="form-text">Manual review is recommended</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="saveApplicationSettings()">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                </div>
            </div>
        </div>

        <!-- Account Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Account Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Account Information</h6>
                        <p><strong>Username:</strong> <span id="account-username">${currentUser?.username || 'Loading...'}</span></p>
                        <p><strong>Email:</strong> <span id="account-email">${currentUser?.email || 'Loading...'}</span></p>
                        <p><strong>Member since:</strong> <span id="account-created">${currentUser?.created_at ? formatDate(currentUser.created_at) : 'Loading...'}</span></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Actions</h6>
                        <button type="button" class="btn btn-outline-primary btn-sm mb-2" onclick="changePassword()">
                            <i class="fas fa-key me-1"></i>Change Password
                        </button>
                        <br>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteAccount()">
                            <i class="fas fa-user-times me-1"></i>Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Load current settings
    loadCurrentSettings();
}

/**
 * Load current settings
 */
async function loadCurrentSettings() {
    try {
        // Check if LinkedIn credentials are configured
        if (currentUser && currentUser.has_linkedin_credentials) {
            document.getElementById('credentials-status').checked = true;
        }
        
    } catch (error) {
        console.error('Failed to load settings:', error);
    }
}

/**
 * Save LinkedIn credentials
 */
async function saveLinkedInCredentials(event) {
    event.preventDefault();
    
    const spinner = document.getElementById('credentials-spinner');
    const submitButton = event.target.querySelector('button[type="submit"]');
    
    try {
        spinner.classList.remove('d-none');
        submitButton.disabled = true;
        
        const credentials = {
            email: document.getElementById('linkedin-email').value,
            password: document.getElementById('linkedin-password').value
        };
        
        await apiCall('/auth/linkedin-credentials', 'POST', credentials);
        
        showNotification('LinkedIn credentials saved successfully', 'success');
        document.getElementById('credentials-status').checked = true;
        
        // Clear form
        document.getElementById('linkedin-credentials-form').reset();
        
    } catch (error) {
        showNotification('Failed to save credentials: ' + error.message, 'error');
    } finally {
        spinner.classList.add('d-none');
        submitButton.disabled = false;
    }
}

/**
 * Delete LinkedIn credentials
 */
async function deleteLinkedInCredentials() {
    if (!confirm('Are you sure you want to delete your LinkedIn credentials?')) {
        return;
    }
    
    try {
        await apiCall('/auth/linkedin-credentials', 'DELETE');
        showNotification('LinkedIn credentials deleted successfully', 'success');
        document.getElementById('credentials-status').checked = false;
        
    } catch (error) {
        showNotification('Failed to delete credentials: ' + error.message, 'error');
    }
}

/**
 * Save application settings
 */
function saveApplicationSettings() {
    showNotification('Application settings saved successfully', 'success');
}

/**
 * Change password
 */
function changePassword() {
    showNotification('Change password feature coming soon!', 'info');
}

/**
 * Delete account
 */
function deleteAccount() {
    showNotification('Delete account feature coming soon!', 'info');
}
