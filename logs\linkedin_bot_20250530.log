2025-05-30 22:24:46 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-30 22:24:46 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-30 22:24:46 - app.main - INFO - lifespan:29 - Database initialized
2025-05-30 22:39:33 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-30 22:39:34 - app.api.auth - INFO - register:118 - New user registered: <EMAIL>
2025-05-30 22:39:49 - app.api.auth - INFO - login:155 - User logged in: <EMAIL>
2025-05-30 22:39:52 - app.api.auth - INFO - login:155 - User logged in: <EMAIL>
2025-05-30 22:39:57 - app.api.auth - INFO - login:155 - User logged in: <EMAIL>
2025-05-30 22:40:06 - app.api.auth - INFO - login:155 - User logged in: <EMAIL>
2025-05-30 22:40:11 - app.api.auth - INFO - login:155 - User logged in: <EMAIL>
2025-05-30 22:41:51 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-30 22:41:55 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-30 22:41:55 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-30 22:41:55 - app.main - INFO - lifespan:29 - Database initialized
2025-05-30 22:42:20 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-30 22:42:20 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-30 22:42:20 - app.main - INFO - lifespan:29 - Database initialized
2025-05-30 22:42:57 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-30 22:42:58 - app.api.auth - INFO - register:118 - New user registered: <EMAIL>
2025-05-30 22:43:04 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 22:46:17 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 22:47:29 - app.api.auth - INFO - register:118 - New user registered: <EMAIL>
2025-05-30 22:47:56 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:09:07 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:09:50 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:15 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:21 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:28 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:29 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:29 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:34 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:36 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:13:46 - app.api.jobs - ERROR - search_jobs:115 - Failed to start job search: 
2025-05-30 23:16:20 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-30 23:16:20 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-30 23:16:20 - app.main - INFO - lifespan:29 - Database initialized
2025-05-30 23:16:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-30 23:16:42 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-30 23:16:53 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:16:59 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature verification failed.
2025-05-30 23:17:12 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:17:12 - app.api.jobs - INFO - search_jobs:97 - Job search started <NAME_EMAIL> with keywords: DevOps Engineer
2025-05-30 23:17:12 - app.api.jobs - INFO - perform_job_search:251 - Starting background job search: search_2_1748668632
2025-05-30 23:17:12 - app.api.jobs - INFO - perform_job_search:322 - Job search completed: 6 jobs found for search search_2_1748668632
2025-05-30 23:17:26 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:22:03 - app.api.jobs - INFO - search_jobs:97 - Job search started <NAME_EMAIL> with keywords: Devops Engineer
2025-05-30 23:22:03 - app.api.jobs - INFO - perform_job_search:251 - Starting background job search: search_1_1748668923
2025-05-30 23:22:03 - app.api.jobs - INFO - perform_job_search:322 - Job search completed: 8 jobs found for search search_1_1748668923
2025-05-30 23:23:11 - app.api.jobs - INFO - search_jobs:97 - Job search started <NAME_EMAIL> with keywords: Devops Engineer
2025-05-30 23:23:11 - app.api.jobs - INFO - perform_job_search:251 - Starting background job search: search_1_1748668991
2025-05-30 23:23:11 - app.api.jobs - INFO - perform_job_search:322 - Job search completed: 8 jobs found for search search_1_1748668991
2025-05-30 23:37:10 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-30 23:38:03 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-30 23:38:03 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-30 23:38:03 - app.main - INFO - lifespan:29 - Database initialized
2025-05-30 23:38:30 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-30 23:38:31 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:38:31 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:39:10 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-30 23:39:48 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:40:02 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:40:11 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:40:27 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:44:07 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:44:12 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:44:12 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:44:30 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:44:30 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:47:53 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:49:33 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:49:46 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:51:36 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:51:43 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:55:15 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-30 23:55:15 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-30 23:55:18 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 00:09:06 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 00:09:34 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:12:23 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 00:13:10 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 00:13:31 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:13:36 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:13:38 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:13:38 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:32:08 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 00:32:08 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 00:41:00 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 00:41:03 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:41:04 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:43:06 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:43:21 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:49:21 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 00:59:51 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:00:11 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:01:07 - app.api.jobs - INFO - search_jobs:97 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 01:01:07 - app.api.jobs - INFO - perform_job_search:251 - Starting background job search: search_3_1748674867
2025-05-31 01:01:07 - app.api.jobs - INFO - perform_job_search:322 - Job search completed: 5 jobs found for search search_3_1748674867
2025-05-31 01:04:24 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:06:25 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:11:42 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 01:14:59 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 01:16:15 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:17:17 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:18:06 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:18:36 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:18:45 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 01:38:53 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
