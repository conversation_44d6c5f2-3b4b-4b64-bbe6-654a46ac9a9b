/**
 * Applications management functionality
 */

let currentApplicationsPage = 1;
let applicationsPerPage = 20;
let currentApplicationsFilter = null;

/**
 * Load applications data
 */
async function loadApplicationsData() {
    try {
        showLoading('applications-view');
        
        // Create applications view if it doesn't exist
        const applicationsView = document.getElementById('applications-view');
        if (!applicationsView.innerHTML.trim()) {
            createApplicationsView();
        }
        
        await Promise.all([
            loadApplicationsList(),
            loadApplicationsStats()
        ]);
        
    } catch (error) {
        console.error('Failed to load applications data:', error);
        showNotification('Failed to load applications data', 'error');
    } finally {
        hideLoading('applications-view');
    }
}

/**
 * Create applications view HTML
 */
function createApplicationsView() {
    const applicationsView = document.getElementById('applications-view');
    applicationsView.innerHTML = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Applications</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshApplications()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="applications-stats">
            <!-- Stats will be loaded here -->
        </div>

        <!-- Status Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <label class="form-label mb-0">Filter by Status:</label>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="application-status-filter" onchange="applyApplicationsFilter()">
                            <option value="">All Applications</option>
                            <option value="pending">Pending</option>
                            <option value="submitted">Submitted</option>
                            <option value="under_review">Under Review</option>
                            <option value="interview_scheduled">Interview Scheduled</option>
                            <option value="rejected">Rejected</option>
                            <option value="accepted">Accepted</option>
                            <option value="error">Error</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications List -->
        <div id="applications-list">
            <!-- Applications will be loaded here -->
        </div>

        <!-- Pagination -->
        <div id="applications-pagination" class="d-flex justify-content-center mt-4">
            <!-- Pagination will be loaded here -->
        </div>
    `;
}

/**
 * Load applications statistics
 */
async function loadApplicationsStats() {
    try {
        const stats = await apiCall('/applications/stats');
        
        if (stats) {
            displayApplicationsStats(stats);
        }
        
    } catch (error) {
        console.error('Failed to load applications stats:', error);
    }
}

/**
 * Display applications statistics
 */
function displayApplicationsStats(stats) {
    const container = document.getElementById('applications-stats');
    
    container.innerHTML = `
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Applications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.total_applications}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.pending_applications}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Interviews</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.interviews_scheduled}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.success_rate}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Load applications list
 */
async function loadApplicationsList() {
    try {
        const params = new URLSearchParams({
            page: currentApplicationsPage,
            per_page: applicationsPerPage
        });
        
        if (currentApplicationsFilter) {
            params.append('status_filter', currentApplicationsFilter);
        }
        
        const response = await apiCall(`/applications/?${params}`);
        
        if (response) {
            displayApplicationsList(response.applications);
            displayApplicationsPagination(response);
        }
        
    } catch (error) {
        console.error('Failed to load applications list:', error);
        showNotification('Failed to load applications', 'error');
    }
}

/**
 * Display applications list
 */
function displayApplicationsList(applications) {
    const container = document.getElementById('applications-list');
    
    if (!applications || applications.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No applications found</h4>
                <p class="text-muted">Start applying to jobs to see your applications here.</p>
                <button class="btn btn-primary" onclick="showJobs()">
                    <i class="fas fa-briefcase me-1"></i>Browse Jobs
                </button>
            </div>
        `;
        return;
    }
    
    container.innerHTML = applications.map(app => createApplicationCard(app)).join('');
}

/**
 * Create application card HTML
 */
function createApplicationCard(application) {
    const statusClass = getStatusClass(application.status);
    const statusIcon = getStatusIcon(application.status);
    
    return `
        <div class="card mb-3" data-application-id="${application.id}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h5 class="card-title mb-1">${application.job_title || 'Job Title'}</h5>
                        <h6 class="card-subtitle mb-2 text-muted">${application.company_name || 'Company'}</h6>
                    </div>
                    <div class="application-actions">
                        <span class="status-badge status-${statusClass}">
                            <i class="fas ${statusIcon} me-1"></i>${application.status.replace('_', ' ')}
                        </span>
                        <div class="dropdown ms-2">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="viewApplicationDetails(${application.id})">
                                    <i class="fas fa-eye me-2"></i>View Details
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="updateApplicationStatus(${application.id})">
                                    <i class="fas fa-edit me-2"></i>Update Status
                                </a></li>
                                ${application.manual_review_required ? `
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-warning" href="#" onclick="reviewApplication(${application.id})">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Review Required
                                    </a></li>
                                ` : ''}
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="row text-muted small">
                    <div class="col-md-6">
                        <i class="fas fa-calendar me-1"></i>Applied: ${application.applied_at ? formatDate(application.applied_at) : 'Not yet applied'}
                    </div>
                    <div class="col-md-6">
                        <i class="fas fa-file-alt me-1"></i>Resume: ${application.resume_name || 'No resume'}
                    </div>
                </div>
                
                ${application.error_message ? `
                    <div class="alert alert-danger mt-2 mb-0">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Error:</strong> ${application.error_message}
                    </div>
                ` : ''}
                
                ${application.manual_review_required ? `
                    <div class="alert alert-warning mt-2 mb-0">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Manual Review Required:</strong> This application needs your attention.
                        <button class="btn btn-sm btn-warning ms-2" onclick="reviewApplication(${application.id})">
                            Review Now
                        </button>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

/**
 * Get status CSS class
 */
function getStatusClass(status) {
    const statusMap = {
        'pending': 'pending',
        'submitted': 'submitted',
        'under_review': 'under-review',
        'interview_scheduled': 'interview',
        'rejected': 'rejected',
        'accepted': 'accepted',
        'error': 'rejected'
    };
    return statusMap[status] || 'pending';
}

/**
 * Get status icon
 */
function getStatusIcon(status) {
    const iconMap = {
        'pending': 'fa-clock',
        'submitted': 'fa-paper-plane',
        'under_review': 'fa-search',
        'interview_scheduled': 'fa-calendar-check',
        'rejected': 'fa-times',
        'accepted': 'fa-check',
        'error': 'fa-exclamation-triangle'
    };
    return iconMap[status] || 'fa-clock';
}

/**
 * Display pagination
 */
function displayApplicationsPagination(response) {
    const container = document.getElementById('applications-pagination');
    
    if (response.total <= applicationsPerPage) {
        container.innerHTML = '';
        return;
    }
    
    const totalPages = Math.ceil(response.total / applicationsPerPage);
    const currentPage = response.page;
    
    let paginationHTML = '<nav><ul class="pagination">';
    
    // Previous button
    if (response.has_prev) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="goToApplicationsPage(${currentPage - 1})">Previous</a>
        </li>`;
    }
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<li class="page-item ${i === currentPage ? 'active' : ''}">
            <a class="page-link" href="#" onclick="goToApplicationsPage(${i})">${i}</a>
        </li>`;
    }
    
    // Next button
    if (response.has_next) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="goToApplicationsPage(${currentPage + 1})">Next</a>
        </li>`;
    }
    
    paginationHTML += '</ul></nav>';
    container.innerHTML = paginationHTML;
}

/**
 * Go to specific page
 */
async function goToApplicationsPage(page) {
    currentApplicationsPage = page;
    await loadApplicationsList();
}

/**
 * Apply status filter
 */
async function applyApplicationsFilter() {
    currentApplicationsFilter = document.getElementById('application-status-filter').value || null;
    currentApplicationsPage = 1;
    await loadApplicationsList();
}

/**
 * View application details
 */
async function viewApplicationDetails(applicationId) {
    showNotification('Application details feature coming soon!', 'info');
}

/**
 * Update application status
 */
async function updateApplicationStatus(applicationId) {
    showNotification('Status update feature coming soon!', 'info');
}

/**
 * Review application
 */
async function reviewApplication(applicationId) {
    showNotification('Application review feature coming soon!', 'info');
}

/**
 * Refresh applications
 */
async function refreshApplications() {
    const refreshButton = document.querySelector('[onclick="refreshApplications()"]');
    const originalContent = refreshButton.innerHTML;
    
    try {
        refreshButton.innerHTML = '<i class="fas fa-sync-alt fa-spin me-1"></i>Refreshing...';
        refreshButton.disabled = true;
        
        await Promise.all([
            loadApplicationsList(),
            loadApplicationsStats()
        ]);
        
        showNotification('Applications refreshed successfully', 'success');
        
    } catch (error) {
        showNotification('Failed to refresh applications', 'error');
    } finally {
        refreshButton.innerHTML = originalContent;
        refreshButton.disabled = false;
    }
}
