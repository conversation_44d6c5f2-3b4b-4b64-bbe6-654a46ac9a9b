#!/usr/bin/env python3
"""
Test real LinkedIn automation
"""
import sys
import asyncio
sys.path.append('.')

async def test_real_linkedin_automation():
    """Test the complete LinkedIn automation workflow"""
    try:
        print("🔗 Testing Real LinkedIn Automation...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionL<PERSON>al
        from app.models.user import User
        from app.api.jobs import JobSearchRequest
        
        print("   ✅ Imports successful")
        
        # Get user with LinkedIn credentials
        db = SessionLocal()
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ⚠️  No users with LinkedIn credentials found")
            print("   💡 Please add LinkedIn credentials in the web app Settings")
            return False
            
        print(f"   ✅ Found user with LinkedIn credentials: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Test WebDriver initialization
        print("   🧪 Initializing WebDriver...")
        try:
            await linkedin_bot.initialize_driver()
            print("   ✅ WebDriver initialized successfully")
        except Exception as e:
            print(f"   ❌ WebDriver initialization failed: {e}")
            return False
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Python Developer",
            location="Remote",
            job_type="Full-time",
            date_posted="past_week"
        )
        print("   ✅ Search request created")
        
        # Test LinkedIn job search
        print("   🔍 Starting LinkedIn job search...")
        try:
            jobs_found = await linkedin_bot.search_jobs(search_request)
            print(f"   ✅ LinkedIn job search completed! Found {jobs_found} jobs")
            
            if jobs_found > 0:
                print("   🎉 Real LinkedIn automation is working!")
                return True
            else:
                print("   ⚠️  No jobs found, but automation completed without errors")
                return True
                
        except Exception as e:
            print(f"   ❌ LinkedIn job search failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # Clean up
            if linkedin_bot.driver:
                linkedin_bot.driver.quit()
                print("   ✅ WebDriver closed")
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_job_search_api():
    """Test the job search API endpoint"""
    try:
        print("\n🔍 Testing Job Search API...")
        
        from app.api.jobs import perform_job_search, JobSearchRequest
        from app.core.database import SessionLocal
        from app.models.user import User
        
        # Get user with LinkedIn credentials
        db = SessionLocal()
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ⚠️  No users with LinkedIn credentials found")
            return True  # Not a failure
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Software Engineer",
            location="Remote",
            job_type="Full-time"
        )
        
        print("   ✅ Search request created")
        
        # Test the API function
        print("   🧪 Testing job search API function...")
        
        await perform_job_search(
            "test_api_search_123",
            search_request,
            user.id,
            db
        )
        
        print("   ✅ Job search API completed successfully")
        
        # Check if jobs were created
        from app.models.job import Job
        job_count = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Total jobs in database for user: {job_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ API test failed: {e}")
        return False

def check_linkedin_credentials():
    """Check if any user has LinkedIn credentials"""
    try:
        print("🔑 Checking LinkedIn Credentials...")
        
        from app.core.database import SessionLocal
        from app.models.user import User
        
        db = SessionLocal()
        
        # Check total users
        total_users = db.query(User).count()
        print(f"   📊 Total users: {total_users}")
        
        # Check users with LinkedIn credentials
        users_with_creds = db.query(User).filter(User.linkedin_email.isnot(None)).count()
        print(f"   🔑 Users with LinkedIn credentials: {users_with_creds}")
        
        if users_with_creds == 0:
            print("   ⚠️  No LinkedIn credentials found!")
            print("   💡 To add LinkedIn credentials:")
            print("      1. Go to http://localhost:8000")
            print("      2. Login to your account")
            print("      3. Go to Settings")
            print("      4. Add your LinkedIn email and password")
            print("      5. Click 'Save Credentials'")
            return False
        
        # Show user with credentials
        user_with_creds = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        if user_with_creds:
            print(f"   ✅ User with credentials: {user_with_creds.email}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Credential check failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Real LinkedIn Automation Test")
    print("=" * 60)
    
    # Check LinkedIn credentials first
    creds_ok = check_linkedin_credentials()
    
    if not creds_ok:
        print("\n❌ No LinkedIn credentials found.")
        print("   Please add LinkedIn credentials in the web app Settings first.")
        return
    
    # Test the job search API
    api_ok = await test_job_search_api()
    
    # Test real LinkedIn automation
    automation_ok = await test_real_linkedin_automation()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   LinkedIn Credentials: {'✅' if creds_ok else '❌'}")
    print(f"   Job Search API: {'✅' if api_ok else '❌'}")
    print(f"   LinkedIn Automation: {'✅' if automation_ok else '❌'}")
    
    if creds_ok and api_ok and automation_ok:
        print("\n🎉 SUCCESS! Real LinkedIn automation is working!")
        print("\n🎯 You should now see:")
        print("   ✅ Real LinkedIn jobs (not just mock data)")
        print("   ✅ Hundreds of job results")
        print("   ✅ Working Apply functionality")
        print("\n💡 Try searching for jobs in the web interface now!")
    else:
        print("\n⚠️  Some tests failed. The system will fall back to mock data.")
        print("\n🔧 To fix:")
        if not creds_ok:
            print("   - Add LinkedIn credentials in Settings")
        if not api_ok:
            print("   - Check backend server logs")
        if not automation_ok:
            print("   - Check Chrome browser and internet connection")

if __name__ == "__main__":
    asyncio.run(main())
