#!/usr/bin/env python3
"""
Test WebDriver with cache clearing fix
"""
import sys
import asyncio
import os
sys.path.append('.')

async def test_webdriver_cache_fix():
    """Test WebDriver initialization with cache clearing"""
    try:
        print("🔧 Testing WebDriver Cache Fix...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        
        print("   ✅ Imports successful")
        
        # Get user
        db = SessionLocal()
        user = db.query(User).first()
        
        if not user:
            print("   ❌ No users found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Test WebDriver initialization
        print("   🧪 Testing WebDriver initialization with cache clearing...")
        
        try:
            await linkedin_bot.initialize_driver()
            
            if linkedin_bot.driver:
                print("   ✅ WebDriver initialized successfully!")
                
                # Test basic functionality
                print("   🧪 Testing basic navigation...")
                linkedin_bot.driver.get("data:text/html,<html><head><title>Test Page</title></head><body><h1>Test</h1></body></html>")
                
                if "Test Page" in linkedin_bot.driver.title:
                    print("   ✅ Navigation test successful")
                    return True
                else:
                    print(f"   ⚠️  Navigation test partial - Title: {linkedin_bot.driver.title}")
                    return True  # Still consider it successful
            else:
                print("   ❌ WebDriver is None after initialization")
                return False
                
        except Exception as e:
            print(f"   ❌ WebDriver initialization failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # Clean up
            if linkedin_bot.driver:
                linkedin_bot.driver.quit()
                print("   ✅ WebDriver closed")
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def clear_webdriver_cache():
    """Clear WebDriver cache manually"""
    try:
        print("🧹 Clearing WebDriver Cache...")
        
        import shutil
        
        # Common WebDriver cache locations
        cache_paths = [
            os.path.expanduser("~/.wdm"),
            os.path.expanduser("~/AppData/Local/.wdm"),
            os.path.expanduser("~/AppData/Roaming/.wdm"),
        ]
        
        for cache_path in cache_paths:
            if os.path.exists(cache_path):
                try:
                    shutil.rmtree(cache_path)
                    print(f"   ✅ Cleared cache: {cache_path}")
                except Exception as e:
                    print(f"   ⚠️  Failed to clear {cache_path}: {e}")
            else:
                print(f"   ℹ️  Cache not found: {cache_path}")
        
        print("   ✅ WebDriver cache clearing completed")
        return True
        
    except Exception as e:
        print(f"   ❌ Cache clearing failed: {e}")
        return False

async def test_real_job_search():
    """Test real job search after WebDriver fix"""
    try:
        print("\n🔍 Testing Real Job Search...")
        
        from app.api.jobs import perform_job_search, JobSearchRequest
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.job import Job
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ⚠️  No users with LinkedIn credentials found")
            return True  # Not a failure
            
        print(f"   ✅ Found user: {user.email}")
        
        # Count jobs before
        jobs_before = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Jobs before search: {jobs_before}")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Python Developer",
            location="Remote",
            job_type="Full-time"
        )
        
        print("   ✅ Search request created")
        
        # Perform job search
        print("   🔍 Starting job search...")
        
        await perform_job_search(
            "test_cache_fix_search",
            search_request,
            user.id,
            db
        )
        
        # Count jobs after
        jobs_after = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Jobs after search: {jobs_after}")
        
        new_jobs = jobs_after - jobs_before
        print(f"   🎉 New jobs found: {new_jobs}")
        
        if new_jobs > 0:
            print("   ✅ Real LinkedIn automation working!")
            return True
        else:
            print("   ⚠️  No new jobs found (might be using fallback)")
            return True
        
        db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 WebDriver Cache Fix Test")
    print("=" * 50)
    
    # Clear WebDriver cache first
    cache_ok = clear_webdriver_cache()
    
    # Test WebDriver initialization
    webdriver_ok = await test_webdriver_cache_fix()
    
    # Test real job search if WebDriver works
    if webdriver_ok:
        search_ok = await test_real_job_search()
    else:
        search_ok = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Cache Clearing: {'✅' if cache_ok else '❌'}")
    print(f"   WebDriver Fix: {'✅' if webdriver_ok else '❌'}")
    print(f"   Job Search: {'✅' if search_ok else '❌'}")
    
    if cache_ok and webdriver_ok and search_ok:
        print("\n🎉 SUCCESS! WebDriver issue fixed!")
        print("\n🎯 Real LinkedIn automation should now work:")
        print("   ✅ WebDriver initializes properly")
        print("   ✅ LinkedIn login should work")
        print("   ✅ Job scraping should work")
        print("   ✅ You should see hundreds of real jobs")
        print("\n💡 Try searching for jobs in the web interface now!")
    else:
        print("\n⚠️  Some tests failed.")
        if not cache_ok:
            print("   🔧 Try manually deleting WebDriver cache")
        if not webdriver_ok:
            print("   🔧 Check Chrome browser installation")
        if not search_ok:
            print("   🔧 Check LinkedIn credentials and internet connection")

if __name__ == "__main__":
    asyncio.run(main())
