#!/usr/bin/env python3
"""
Test improved LinkedIn automation with better error handling
"""
import sys
import asyncio
sys.path.append('.')

async def test_improved_job_search():
    """Test the improved job search functionality"""
    try:
        print("🔍 Testing Improved LinkedIn Job Search...")
        
        from app.api.jobs import perform_job_search, JobSearchRequest
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.job import Job
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Count jobs before
        jobs_before = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Jobs before search: {jobs_before}")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Python Developer",
            location="Remote",
            job_type="Full-time"
        )
        
        print("   ✅ Search request created")
        print(f"   🔍 Searching for: {search_request.keywords}")
        print(f"   📍 Location: {search_request.location}")
        
        # Perform job search
        print("   🚀 Starting improved job search...")
        
        search_id = f"test_improved_{int(asyncio.get_event_loop().time())}"
        
        await perform_job_search(
            search_id,
            search_request,
            user.id,
            db
        )
        
        # Count jobs after
        jobs_after = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Jobs after search: {jobs_after}")
        
        new_jobs = jobs_after - jobs_before
        print(f"   🎉 New jobs found: {new_jobs}")
        
        if new_jobs > 0:
            print("   ✅ LinkedIn automation working! Found real jobs!")
            
            # Show some recent jobs
            recent_jobs = db.query(Job).filter(Job.user_id == user.id).order_by(Job.date_posted.desc()).limit(5).all()
            print("   📋 Recent jobs found:")
            for job in recent_jobs:
                print(f"      - {job.title} at {job.company}")
                if job.location:
                    print(f"        📍 {job.location}")
                print(f"        🔗 {job.url}")
                print(f"        ⚡ Easy Apply: {'Yes' if job.easy_apply else 'No'}")
                print()
            
            return True
        else:
            print("   ⚠️  No new jobs found")
            print("   💡 This could mean:")
            print("      - All jobs for this search were already in database")
            print("      - LinkedIn changed their page structure")
            print("      - Search filters were too restrictive")
            print("      - Network or browser issues")
            return False
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db' in locals():
            db.close()

async def test_direct_linkedin_bot():
    """Test LinkedIn bot directly"""
    try:
        print("\n🤖 Testing LinkedIn Bot Directly...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.api.jobs import JobSearchRequest
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="DevOps Engineer",
            location="San Francisco",
            job_type="Full-time"
        )
        
        print(f"   🔍 Testing search: {search_request.keywords}")
        
        # Test job search
        try:
            jobs_found = await linkedin_bot.search_jobs(search_request)
            
            if jobs_found > 0:
                print(f"   ✅ Direct bot test successful! Found {jobs_found} jobs")
                return True
            else:
                print("   ⚠️  Direct bot test found 0 jobs")
                return False
                
        except Exception as e:
            print(f"   ❌ Direct bot test failed: {e}")
            return False
        
        finally:
            # Clean up
            linkedin_bot.close()
            db.close()
        
    except Exception as e:
        print(f"   ❌ Direct bot test setup failed: {e}")
        return False

def check_recent_logs():
    """Check recent logs for insights"""
    try:
        print("\n📋 Checking Recent Logs...")
        
        import os
        from datetime import datetime
        
        log_file = "logs/linkedin_bot_20250531.log"
        
        if not os.path.exists(log_file):
            print("   ⚠️  Log file not found")
            return
        
        # Read last 20 lines
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            recent_lines = lines[-20:] if len(lines) > 20 else lines
        
        print("   📄 Recent log entries:")
        for line in recent_lines:
            line = line.strip()
            if line:
                # Highlight important log levels
                if "ERROR" in line:
                    print(f"   ❌ {line}")
                elif "WARNING" in line:
                    print(f"   ⚠️  {line}")
                elif "INFO" in line and ("login" in line.lower() or "search" in line.lower() or "found" in line.lower()):
                    print(f"   ✅ {line}")
                else:
                    print(f"   ℹ️  {line}")
        
    except Exception as e:
        print(f"   ❌ Failed to read logs: {e}")

async def main():
    """Run all tests"""
    print("🧪 Improved LinkedIn Automation Test")
    print("=" * 60)
    
    # Check recent logs first
    check_recent_logs()
    
    # Test improved job search via API
    api_test_ok = await test_improved_job_search()
    
    # Test LinkedIn bot directly
    direct_test_ok = await test_direct_linkedin_bot()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   API Job Search: {'✅' if api_test_ok else '❌'}")
    print(f"   Direct Bot Test: {'✅' if direct_test_ok else '❌'}")
    
    if api_test_ok or direct_test_ok:
        print("\n🎉 SUCCESS! LinkedIn automation is working!")
        print("\n🎯 What this means:")
        print("   ✅ WebDriver is stable and working")
        print("   ✅ LinkedIn login is successful")
        print("   ✅ Job search and scraping is functional")
        print("   ✅ Real LinkedIn jobs are being found and saved")
        print("\n💡 You can now:")
        print("   1. Use the web interface to search for jobs")
        print("   2. See hundreds of real LinkedIn jobs")
        print("   3. Apply to jobs with the automation")
        print("   4. Customize resumes and cover letters")
    else:
        print("\n⚠️  Tests failed. Possible issues:")
        print("   🔧 Check if 2FA is still enabled on LinkedIn")
        print("   🔧 Check internet connection")
        print("   🔧 Check if LinkedIn changed their page structure")
        print("   🔧 Check server logs for detailed error messages")
        print("\n💡 Try:")
        print("   1. Restart the backend server")
        print("   2. Clear browser cache and cookies")
        print("   3. Try a different search term")

if __name__ == "__main__":
    asyncio.run(main())
