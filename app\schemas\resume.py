"""
Resume-related Pydantic schemas
"""

from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class ResumeBase(BaseModel):
    """Base resume schema"""
    name: str
    filename: str
    file_type: str
    is_original: bool = True


class ResumeCreate(ResumeBase):
    """Schema for creating a resume"""
    pass


class ResumeResponse(ResumeBase):
    """Schema for resume responses"""
    id: int
    times_used: int = 0
    created_at: str
    last_used: Optional[str] = None

    class Config:
        from_attributes = True


class ResumeUploadResponse(BaseModel):
    """Schema for resume upload response"""
    message: str
    filename: str
    size: int


class ResumeDeleteResponse(BaseModel):
    """Schema for resume deletion response"""
    message: str


class ResumeDownloadResponse(BaseModel):
    """Schema for resume download response"""
    message: str
    resume_id: int
