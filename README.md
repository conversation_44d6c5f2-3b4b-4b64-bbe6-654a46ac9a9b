# LinkedIn Automation Bot POC

A comprehensive, scalable proof-of-concept for LinkedIn job application automation with AI-powered resume customization, secure credential management, and real-time tracking through a modern web interface.

## 🚀 Features

### Core Functionality
- **🔐 Secure Credential Management**: AES encryption for LinkedIn credentials
- **🤖 Intelligent Job Search**: Automated LinkedIn job discovery with customizable criteria
- **🧠 AI-Powered Resume Customization**: GPT-4o integration for tailored resumes
- **📊 Real-time Dashboard**: Live tracking of jobs, applications, and success metrics
- **✅ Manual Confirmation System**: Review and approve applications before submission
- **📈 Analytics & Reporting**: Comprehensive application tracking and success analytics

### Technical Architecture
- **🌐 FastAPI Backend**: Modern, async Python web framework
- **💾 SQLite Database**: Local data persistence with proper schema design
- **🎨 Responsive Web UI**: Bootstrap-based dashboard with real-time updates
- **🔒 JWT Authentication**: Secure user sessions and API access
- **🐳 Docker Support**: Easy deployment with containerization
- **📝 Comprehensive Logging**: Detailed logging for debugging and monitoring

### AI Integration
- **Resume Analysis**: Extract skills and experience from uploaded resumes
- **Job Matching**: Calculate compatibility scores between resumes and job descriptions
- **Content Customization**: Intelligently modify resumes for specific job requirements
- **Cover Letter Generation**: AI-generated personalized cover letters

## 📋 Prerequisites

- **Python 3.8+** (3.11 recommended)
- **Chrome Browser** (for LinkedIn automation)
- **OpenAI API Key** (for AI features)
- **LinkedIn Account** (for job searching)

## 🛠️ Quick Setup

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd linkedin-automation-bot

# Run the setup script
python setup.py
```

The setup script will:
- Install all dependencies
- Generate secure encryption keys
- Create configuration files
- Initialize the database
- Guide you through API key configuration

### Option 2: Manual Setup

1. **Install Dependencies**
```bash
pip install -r requirements.txt
```

2. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Initialize Database**
```bash
python -c "from app.core.database import init_db; import asyncio; asyncio.run(init_db())"
```

### Option 3: Docker Setup

```bash
# Copy environment file
cp .env.example .env
# Edit .env with your configuration

# Start with Docker Compose
docker-compose up -d
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Security
SECRET_KEY=your-secret-key
ENCRYPTION_KEY=your-encryption-key

# OpenAI Integration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# Application Settings
MAX_APPLICATIONS_PER_DAY=10
HEADLESS_BROWSER=true
DEBUG=false
```

### LinkedIn Credentials

LinkedIn credentials can be configured:
1. **Via Web Interface** (Recommended): Set credentials securely through the dashboard
2. **Via Environment Variables**: Set `LINKEDIN_EMAIL` and `LINKEDIN_PASSWORD` in `.env`

## 🚀 Usage

### Starting the Application

**Development Mode:**
```bash
python -m uvicorn app.main:app --reload
```

**Production Mode:**
```bash
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

**Docker:**
```bash
docker-compose up -d
```

### Accessing the Dashboard

1. Open your browser to `http://localhost:8000`
2. Create an account or sign in
3. Configure your LinkedIn credentials in Settings
4. Upload your resume
5. Start searching for jobs!

## 📱 Web Interface Guide

### Dashboard
- **Overview Statistics**: Total jobs found, applications submitted, success rate
- **Recent Activity**: Real-time feed of job discoveries and application updates
- **Job Trends**: Visual charts showing activity over time
- **Pending Reviews**: Applications requiring manual confirmation

### Job Search
- **Search Configuration**: Keywords, location, date posted, job type
- **Automated Discovery**: Background job searching with configurable frequency
- **Smart Filtering**: AI-powered job relevance scoring

### Applications
- **Application Management**: Track all job applications and their status
- **Manual Review**: Approve or modify applications before submission
- **Status Tracking**: Monitor application progress from submission to response

### Resume Management
- **File Upload**: Support for PDF and DOCX formats
- **Version Control**: Automatic versioning of customized resumes
- **AI Customization**: Generate tailored resumes for specific job postings

## 🏗️ Project Structure

```
linkedin-automation-bot/
├── app/
│   ├── api/                 # FastAPI route handlers
│   │   ├── auth.py         # Authentication endpoints
│   │   ├── jobs.py         # Job management endpoints
│   │   ├── applications.py # Application endpoints
│   │   └── dashboard.py    # Dashboard data endpoints
│   ├── core/               # Core application components
│   │   ├── config.py       # Configuration management
│   │   ├── database.py     # Database connection and setup
│   │   ├── security.py     # Encryption and authentication
│   │   └── logging_config.py # Logging configuration
│   ├── models/             # Database models
│   │   ├── user.py         # User model
│   │   ├── job.py          # Job model
│   │   ├── application.py  # Application model
│   │   └── resume.py       # Resume model
│   ├── services/           # Business logic services
│   │   ├── linkedin_bot.py # LinkedIn automation service
│   │   ├── ai_service.py   # OpenAI integration service
│   │   ├── job_service.py  # Job management service
│   │   ├── application_service.py # Application processing
│   │   └── resume_service.py # Resume management
│   └── main.py             # FastAPI application entry point
├── frontend/
│   ├── templates/          # HTML templates
│   │   └── index.html      # Main dashboard template
│   └── static/             # Static assets
│       ├── css/            # Stylesheets
│       └── js/             # JavaScript files
├── uploads/                # Resume file storage
├── logs/                   # Application logs
├── requirements.txt        # Python dependencies
├── docker-compose.yml      # Docker configuration
├── Dockerfile             # Docker image definition
├── setup.py               # Automated setup script
└── .env.example           # Environment configuration template
```

## 🔌 API Documentation

The application provides a comprehensive REST API for all functionality:

### Authentication Endpoints
- `POST /api/auth/register` - Create new user account
- `POST /api/auth/login` - User authentication
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/linkedin-credentials` - Save LinkedIn credentials
- `DELETE /api/auth/linkedin-credentials` - Remove LinkedIn credentials

### Job Management
- `POST /api/jobs/search` - Start job search process
- `GET /api/jobs/` - Get paginated job listings
- `GET /api/jobs/{job_id}` - Get specific job details
- `POST /api/jobs/{job_id}/bookmark` - Bookmark/unbookmark job
- `DELETE /api/jobs/{job_id}` - Delete job

### Application Management
- `POST /api/applications/` - Create new application
- `GET /api/applications/` - Get user's applications
- `GET /api/applications/stats` - Get application statistics
- `PUT /api/applications/{id}/status` - Update application status

### Dashboard & Analytics
- `GET /api/dashboard/` - Get comprehensive dashboard data
- `GET /api/dashboard/notifications` - Get real-time notifications

## 🧪 Testing

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_auth.py
```

### Test Structure

```
tests/
├── test_auth.py           # Authentication tests
├── test_jobs.py           # Job management tests
├── test_applications.py   # Application tests
├── test_ai_service.py     # AI service tests
└── conftest.py           # Test configuration
```

## 🔒 Security Considerations

### Data Protection
- **Encryption**: All sensitive data (LinkedIn credentials) encrypted with AES
- **Secure Storage**: Credentials never stored in plain text
- **JWT Tokens**: Secure session management with expiring tokens
- **Input Validation**: Comprehensive input sanitization and validation

### LinkedIn Compliance
- **Rate Limiting**: Built-in delays to respect LinkedIn's rate limits
- **Human-like Behavior**: Random delays and realistic interaction patterns
- **Manual Review**: All applications require manual confirmation by default
- **Respectful Automation**: Designed to complement, not replace, human job searching

### Best Practices
- **Environment Variables**: Sensitive configuration via environment variables
- **Logging**: Comprehensive logging without exposing sensitive data
- **Error Handling**: Graceful error handling and user feedback
- **Database Security**: Parameterized queries to prevent SQL injection

## 🚨 Important Legal and Ethical Considerations

### ⚠️ Terms of Service Compliance
- **LinkedIn ToS**: Using automation tools may violate LinkedIn's Terms of Service
- **Account Risk**: Automated activity could result in account suspension or termination
- **Use at Your Own Risk**: This tool is provided for educational and research purposes

### 🎯 Recommended Usage
- **Manual Review**: Always review applications before submission
- **Reasonable Limits**: Limit daily applications to reasonable numbers (5-10)
- **Quality over Quantity**: Focus on relevant, high-quality applications
- **Human Oversight**: Maintain human involvement in the job search process

### 📋 Ethical Guidelines
- **Truthful Applications**: Never submit false or misleading information
- **Respect Employers**: Don't spam employers with irrelevant applications
- **Professional Conduct**: Maintain professional standards in all interactions
- **Data Privacy**: Respect privacy of job postings and employer information

## 🛠️ Development

### Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd linkedin-automation-bot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Set up pre-commit hooks
pre-commit install

# Run in development mode
python -m uvicorn app.main:app --reload
```

### Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Run tests**: `pytest`
5. **Commit changes**: `git commit -m 'Add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Code Style
- **Python**: Follow PEP 8 guidelines
- **Type Hints**: Use type hints for all functions
- **Documentation**: Document all public functions and classes
- **Testing**: Maintain test coverage above 80%

## 🐛 Troubleshooting

### Common Issues

**1. Chrome Driver Issues**
```bash
# Update Chrome driver
pip install --upgrade webdriver-manager
```

**2. Database Connection Errors**
```bash
# Reset database
rm linkedin_bot.db
python -c "from app.core.database import init_db; import asyncio; asyncio.run(init_db())"
```

**3. OpenAI API Errors**
- Verify your API key in `.env`
- Check your OpenAI account credits
- Ensure you have access to GPT-4 model

**4. LinkedIn Login Issues**
- Check credentials in Settings
- Verify 2FA settings
- Try manual login first to check for captchas

### Getting Help

- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Join community discussions
- **Documentation**: Check the wiki for detailed guides
- **Support**: Contact maintainers for critical issues

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **FastAPI**: Modern web framework for building APIs
- **OpenAI**: AI-powered resume customization
- **Selenium**: Web automation capabilities
- **Bootstrap**: Responsive UI components
- **Chart.js**: Beautiful data visualizations

## 📊 Roadmap

### Version 2.0 (Planned)
- [ ] Multi-platform job board support (Indeed, Glassdoor)
- [ ] Advanced AI job matching algorithms
- [ ] Email integration for application tracking
- [ ] Mobile-responsive PWA
- [ ] Team collaboration features
- [ ] Advanced analytics and reporting

### Version 1.1 (Next Release)
- [ ] Resume template library
- [ ] Interview scheduling integration
- [ ] Application follow-up automation
- [ ] Enhanced error recovery
- [ ] Performance optimizations

---

**⚠️ Disclaimer**: This tool is for educational and research purposes. Users are responsible for complying with LinkedIn's Terms of Service and applicable laws. Use responsibly and ethically.