# LinkedIn Job Application Bot

This bot automates the process of searching for jobs on LinkedIn, analyzing job descriptions, customizing resumes, and applying to jobs using LinkedIn's Easy Apply feature.

## Features

- LinkedIn authentication
- Job search with customizable criteria
- Job listing extraction and filtering
- Job description analysis
- Resume customization based on job requirements
- Automated application using LinkedIn's Easy Apply

## Setup

### Prerequisites

- Python 3.7 or higher
- Chrome browser installed

### Installation

1. Clone this repository or download the files
2. Install the required packages:

```bash
pip install -r requirements.txt
```

3. Edit the `config.ini` file with your LinkedIn credentials and job search preferences

### Configuration

Edit the `config.ini` file to customize:

- LinkedIn login credentials
- Browser settings
- Job search criteria
- Resume path and keywords
- Common application answers

## Usage

Run the bot with:

```bash
python linkedin_job_bot.py
```

Optional arguments:
- `--config`: Specify a custom config file path (default: config.ini)
- `--save-csv`: Save job listings to a CSV file

Example:
```bash
python linkedin_job_bot.py --config my_config.ini --save-csv
```

## Important Notes

- Using automation tools with LinkedIn may violate their Terms of Service
- Use responsibly and at your own risk
- Add random delays between actions to mimic human behavior
- LinkedIn's website structure may change, which could break the selectors used in this script

## Customization

- Add more application field mappings in the `[ApplicationAnswers]` section of `config.ini`
- Modify the script to handle specific application workflows you commonly encounter