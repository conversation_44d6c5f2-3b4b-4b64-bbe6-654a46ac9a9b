#!/usr/bin/env python3
"""
Test if server is serving static files correctly
"""
import requests
import time

def test_server_endpoints():
    """Test various server endpoints"""
    print("🔍 Testing Server Endpoints...")
    
    endpoints = [
        ('/', 'Main page'),
        ('/static/js/main.js', 'Main JavaScript'),
        ('/static/js/auth.js', 'Auth JavaScript'),
        ('/static/js/dashboard.js', 'Dashboard JavaScript'),
        ('/static/css/dashboard.css', 'Dashboard CSS'),
        ('/docs', 'API Documentation'),
        ('/api/auth/me', 'Auth endpoint (should fail without token)'),
    ]
    
    for endpoint, description in endpoints:
        try:
            print(f"\n📡 Testing {description}: {endpoint}")
            
            # Set a reasonable timeout
            response = requests.get(f'http://localhost:8000{endpoint}', timeout=5)
            
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'Not specified')}")
            print(f"   Content Length: {len(response.content)} bytes")
            
            if response.status_code == 200:
                print("   ✅ Success")
                
                # Check content for JavaScript files
                if endpoint.endswith('.js'):
                    content = response.text
                    if 'function' in content or 'const' in content or 'var' in content:
                        print("   ✅ JavaScript content detected")
                    else:
                        print("   ❌ No JavaScript content found")
                        print(f"   Content preview: {content[:100]}...")
                        
            elif response.status_code == 401 and 'auth' in endpoint:
                print("   ✅ Expected 401 for auth endpoint")
            else:
                print(f"   ❌ Unexpected status code")
                
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout - server not responding")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection failed - server not running")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_main_page_content():
    """Test the main page content specifically"""
    print("\n🔍 Testing Main Page Content...")
    
    try:
        response = requests.get('http://localhost:8000/', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for key elements
            checks = [
                ('<!DOCTYPE html>', 'HTML doctype'),
                ('<title>', 'Page title'),
                ('LinkedIn Automation Bot', 'App title'),
                ('/static/js/main.js', 'Main JS reference'),
                ('/static/js/auth.js', 'Auth JS reference'),
                ('bootstrap', 'Bootstrap CSS'),
                ('DOMContentLoaded', 'DOM ready handler'),
            ]
            
            print("   Content checks:")
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ Missing: {description}")
            
            print(f"\n   Total content length: {len(content)} characters")
            
            # Show first 500 characters
            print(f"\n   Content preview:")
            print(f"   {content[:500]}...")
            
        else:
            print(f"   ❌ Status code: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Server Static Files Test")
    print("=" * 50)
    
    test_server_endpoints()
    test_main_page_content()
    
    print("\n" + "=" * 50)
    print("💡 If static files are not loading:")
    print("   1. Check if server is running: python -m uvicorn app.main:app --reload")
    print("   2. Check server logs for errors")
    print("   3. Try restarting the server")
    print("   4. Check browser console for JavaScript errors")
