"""
Resume management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from datetime import datetime

from app.core.database import get_db
from app.models.user import User
from app.api.auth import get_current_user
from app.schemas.resume import ResumeResponse, ResumeCreate

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[ResumeResponse])
async def get_resumes(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all resumes for the current user"""
    try:
        # For POC, return mock data since we don't have resume model yet
        mock_resumes = [
            {
                "id": 1,
                "name": "Software Engineer Resume",
                "filename": "resume_2024.pdf",
                "file_type": "pdf",
                "is_original": True,
                "times_used": 5,
                "created_at": datetime.utcnow().isoformat(),
                "last_used": datetime.utcnow().isoformat()
            },
            {
                "id": 2,
                "name": "DevOps Engineer Resume",
                "filename": "devops_resume.pdf",
                "file_type": "pdf",
                "is_original": False,
                "times_used": 2,
                "created_at": datetime.utcnow().isoformat(),
                "last_used": None
            }
        ]
        
        logger.info("Retrieved %d resumes for user %s", len(mock_resumes), current_user.email)
        return mock_resumes
        
    except Exception as e:
        logger.error("Failed to get resumes for user %s: %s", current_user.email, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve resumes"
        )


@router.post("/upload")
async def upload_resume(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload a new resume"""
    try:
        # Validate file type
        allowed_types = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only PDF and DOCX files are allowed"
            )
        
        # Validate file size (10MB limit)
        max_size = 10 * 1024 * 1024
        content = await file.read()
        if len(content) > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File size must be less than 10MB"
            )
        
        # For POC, just return success without actually storing the file
        logger.info("Resume uploaded successfully for user %s: %s", current_user.email, file.filename)
        
        return {
            "message": "Resume uploaded successfully",
            "filename": file.filename,
            "size": len(content)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to upload resume for user %s: %s", current_user.email, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload resume"
        )


@router.get("/{resume_id}", response_model=ResumeResponse)
async def get_resume(
    resume_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific resume"""
    try:
        # For POC, return mock data
        mock_resume = {
            "id": resume_id,
            "name": f"Resume {resume_id}",
            "filename": f"resume_{resume_id}.pdf",
            "file_type": "pdf",
            "is_original": True,
            "times_used": 3,
            "created_at": datetime.utcnow().isoformat(),
            "last_used": datetime.utcnow().isoformat()
        }
        
        logger.info("Retrieved resume %d for user %s", resume_id, current_user.email)
        return mock_resume
        
    except Exception as e:
        logger.error("Failed to get resume %d for user %s: %s", resume_id, current_user.email, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve resume"
        )


@router.delete("/{resume_id}")
async def delete_resume(
    resume_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a resume"""
    try:
        # For POC, just return success
        logger.info("Resume %d deleted for user %s", resume_id, current_user.email)
        
        return {"message": "Resume deleted successfully"}
        
    except Exception as e:
        logger.error("Failed to delete resume %d for user %s: %s", resume_id, current_user.email, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete resume"
        )


@router.post("/{resume_id}/download")
async def download_resume(
    resume_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Download a resume"""
    try:
        # For POC, just return a message
        logger.info("Resume %d download requested for user %s", resume_id, current_user.email)
        
        return {"message": "Download feature coming soon!", "resume_id": resume_id}
        
    except Exception as e:
        logger.error("Failed to download resume %d for user %s: %s", resume_id, current_user.email, str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download resume"
        )
