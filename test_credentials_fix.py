#!/usr/bin/env python3
"""
Test credentials decryption fix
"""
import sys
import asyncio
sys.path.append('.')

def test_credentials_decryption():
    """Test if LinkedIn credentials can be decrypted properly"""
    try:
        print("🔑 Testing Credentials Decryption...")
        
        from app.core.database import <PERSON><PERSON>ocal
        from app.models.user import User
        from app.core.security import decrypt_credentials
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Test decryption
        try:
            credentials = decrypt_credentials(user.linkedin_email, user.linkedin_password)
            
            if credentials["email"] and credentials["password"]:
                print(f"   ✅ Credentials decrypted successfully!")
                print(f"   📧 LinkedIn email: {credentials['email']}")
                print(f"   🔒 LinkedIn password: {'*' * len(credentials['password'])}")
                return True
            else:
                print("   ❌ Decrypted credentials are empty")
                return False
                
        except Exception as e:
            print(f"   ❌ Decryption failed: {e}")
            return False
        
        finally:
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

async def test_linkedin_login():
    """Test LinkedIn login with decrypted credentials"""
    try:
        print("\n🔗 Testing LinkedIn Login...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Initialize WebDriver
        try:
            await linkedin_bot.initialize_driver()
            print("   ✅ WebDriver initialized")
        except Exception as e:
            print(f"   ❌ WebDriver initialization failed: {e}")
            return False
        
        # Test LinkedIn login
        try:
            print("   🧪 Testing LinkedIn login...")
            success = await linkedin_bot.login()
            
            if success:
                print("   ✅ LinkedIn login successful!")
                return True
            else:
                print("   ❌ LinkedIn login failed")
                return False
                
        except Exception as e:
            print(f"   ❌ LinkedIn login error: {e}")
            return False
        
        finally:
            # Clean up
            if linkedin_bot.driver:
                linkedin_bot.driver.quit()
                print("   ✅ WebDriver closed")
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

async def test_full_job_search():
    """Test full job search with real LinkedIn automation"""
    try:
        print("\n🔍 Testing Full Job Search...")
        
        from app.api.jobs import perform_job_search, JobSearchRequest
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.job import Job
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Count jobs before search
        jobs_before = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Jobs before search: {jobs_before}")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Python Developer",
            location="Remote",
            job_type="Full-time",
            date_posted="past_week"
        )
        
        print("   ✅ Search request created")
        
        # Perform job search
        print("   🔍 Starting job search...")
        
        await perform_job_search(
            "test_full_search_123",
            search_request,
            user.id,
            db
        )
        
        # Count jobs after search
        jobs_after = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Jobs after search: {jobs_after}")
        
        new_jobs = jobs_after - jobs_before
        print(f"   🎉 New jobs found: {new_jobs}")
        
        if new_jobs > 0:
            print("   ✅ Job search successful - real LinkedIn automation working!")
            return True
        else:
            print("   ⚠️  No new jobs found, but search completed")
            return True
        
        db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Credentials Fix Test")
    print("=" * 50)
    
    # Test credentials decryption
    decrypt_ok = test_credentials_decryption()
    
    if not decrypt_ok:
        print("\n❌ Credentials decryption failed.")
        print("   The encryption key might be incorrect.")
        return
    
    # Test LinkedIn login
    login_ok = await test_linkedin_login()
    
    # Test full job search
    search_ok = await test_full_job_search()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Credentials Decryption: {'✅' if decrypt_ok else '❌'}")
    print(f"   LinkedIn Login: {'✅' if login_ok else '❌'}")
    print(f"   Full Job Search: {'✅' if search_ok else '❌'}")
    
    if decrypt_ok and login_ok and search_ok:
        print("\n🎉 SUCCESS! Real LinkedIn automation is fully working!")
        print("\n🎯 You should now see:")
        print("   ✅ Real LinkedIn jobs (hundreds of results)")
        print("   ✅ Successful LinkedIn login and scraping")
        print("   ✅ Working Apply functionality")
        print("\n💡 Try searching for jobs in the web interface now!")
        print("   You should see many more than 5 results!")
    else:
        print("\n⚠️  Some tests failed.")
        if not decrypt_ok:
            print("   🔧 Fix: Update ENCRYPTION_KEY in .env file")
        if not login_ok:
            print("   🔧 Fix: Check LinkedIn credentials and internet connection")
        if not search_ok:
            print("   🔧 Fix: Check backend logs for errors")

if __name__ == "__main__":
    asyncio.run(main())
