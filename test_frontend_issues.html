<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Testing Suite</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .test-result { margin: 5px 0; }
        button { margin: 5px; padding: 10px; }
        #console-output { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: scroll; font-family: monospace; }
    </style>
</head>
<body>
    <h1>LinkedIn Automation Bot - Frontend Testing Suite</h1>
    
    <div class="test-section">
        <h2>1. API Connectivity Tests</h2>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testDashboard()">Test Dashboard API</button>
        <button onclick="testJobs()">Test Jobs API</button>
        <button onclick="testApplications()">Test Applications API</button>
        <button onclick="testResumes()">Test Resumes API</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>2. Frontend JavaScript Tests</h2>
        <button onclick="testJavaScriptLoading()">Test JS Loading</button>
        <button onclick="testViewSwitching()">Test View Switching</button>
        <button onclick="testDataRendering()">Test Data Rendering</button>
        <div id="js-results"></div>
    </div>

    <div class="test-section">
        <h2>3. Integration Tests</h2>
        <button onclick="testFullWorkflow()">Test Full Workflow</button>
        <button onclick="testTabNavigation()">Test Tab Navigation</button>
        <div id="integration-results"></div>
    </div>

    <div class="test-section">
        <h2>4. Console Output</h2>
        <button onclick="clearConsole()">Clear Console</button>
        <div id="console-output"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = null;
        
        function log(message, type = 'info') {
            const console_output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            console_output.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console_output.scrollTop = console_output.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }

        function updateResults(sectionId, message, type = 'info') {
            const section = document.getElementById(sectionId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            section.innerHTML += `<div class="test-result ${className}">✓ ${message}</div>`;
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const headers = {};
                
                if (authToken) {
                    headers['Authorization'] = `Bearer ${authToken}`;
                }
                
                if (data) {
                    headers['Content-Type'] = 'application/json';
                }
                
                const config = {
                    method: method,
                    headers: headers
                };
                
                if (data) {
                    config.body = JSON.stringify(data);
                }
                
                log(`Making API call: ${method} ${API_BASE + endpoint}`);
                const response = await fetch(API_BASE + endpoint, config);
                log(`API response status: ${response.status}`);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
                
                const result = await response.json();
                log(`API response received: ${JSON.stringify(result).substring(0, 100)}...`);
                return result;
                
            } catch (error) {
                log(`API call failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testLogin() {
            try {
                log('Testing login...', 'info');
                const result = await apiCall('/auth/login', 'POST', {
                    email: '<EMAIL>',
                    password: 'testpass123'
                });
                
                authToken = result.access_token;
                updateResults('api-results', 'Login: SUCCESS', 'success');
                log('Login successful', 'success');
                
            } catch (error) {
                updateResults('api-results', `Login: FAILED - ${error.message}`, 'error');
                log(`Login failed: ${error.message}`, 'error');
            }
        }

        async function testDashboard() {
            try {
                log('Testing dashboard API...', 'info');
                const result = await apiCall('/dashboard/');
                updateResults('api-results', 'Dashboard API: SUCCESS', 'success');
                log('Dashboard API successful', 'success');
                
            } catch (error) {
                updateResults('api-results', `Dashboard API: FAILED - ${error.message}`, 'error');
                log(`Dashboard API failed: ${error.message}`, 'error');
            }
        }

        async function testJobs() {
            try {
                log('Testing jobs API...', 'info');
                const result = await apiCall('/jobs/?page=1&per_page=20');
                updateResults('api-results', `Jobs API: SUCCESS (${result.jobs?.length || 0} jobs)`, 'success');
                log('Jobs API successful', 'success');
                
            } catch (error) {
                updateResults('api-results', `Jobs API: FAILED - ${error.message}`, 'error');
                log(`Jobs API failed: ${error.message}`, 'error');
            }
        }

        async function testApplications() {
            try {
                log('Testing applications API...', 'info');
                const result = await apiCall('/applications/?page=1&per_page=20');
                updateResults('api-results', `Applications API: SUCCESS (${result.applications?.length || 0} applications)`, 'success');
                log('Applications API successful', 'success');
                
            } catch (error) {
                updateResults('api-results', `Applications API: FAILED - ${error.message}`, 'error');
                log(`Applications API failed: ${error.message}`, 'error');
            }
        }

        async function testResumes() {
            try {
                log('Testing resumes API...', 'info');
                const result = await apiCall('/resumes/');
                updateResults('api-results', `Resumes API: SUCCESS (${result?.length || 0} resumes)`, 'success');
                log('Resumes API successful', 'success');
                
            } catch (error) {
                updateResults('api-results', `Resumes API: FAILED - ${error.message}`, 'error');
                log(`Resumes API failed: ${error.message}`, 'error');
            }
        }

        async function testJavaScriptLoading() {
            log('Testing JavaScript loading...', 'info');
            
            // Test if main functions exist
            const functions = ['switchView', 'apiCall', 'showNotification', 'loadDashboardData', 'loadJobsData', 'loadApplicationsData', 'loadResumesData'];
            let loadedCount = 0;
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    loadedCount++;
                    log(`Function ${func}: LOADED`, 'success');
                } else {
                    log(`Function ${func}: NOT FOUND`, 'error');
                }
            });
            
            updateResults('js-results', `JavaScript Functions: ${loadedCount}/${functions.length} loaded`, loadedCount === functions.length ? 'success' : 'warning');
        }

        async function testViewSwitching() {
            log('Testing view switching...', 'info');
            
            const views = ['dashboard', 'jobs', 'applications', 'resumes'];
            let switchCount = 0;
            
            for (const view of views) {
                try {
                    if (typeof window.switchView === 'function') {
                        window.switchView(view);
                        const viewElement = document.getElementById(`${view}-view`);
                        if (viewElement && !viewElement.classList.contains('d-none')) {
                            switchCount++;
                            log(`View ${view}: SWITCHED SUCCESSFULLY`, 'success');
                        } else {
                            log(`View ${view}: SWITCH FAILED`, 'error');
                        }
                    } else {
                        log(`switchView function not available`, 'error');
                        break;
                    }
                } catch (error) {
                    log(`View ${view}: ERROR - ${error.message}`, 'error');
                }
            }
            
            updateResults('js-results', `View Switching: ${switchCount}/${views.length} successful`, switchCount === views.length ? 'success' : 'warning');
        }

        async function testDataRendering() {
            log('Testing data rendering...', 'info');
            
            // Test if data rendering functions work
            const mockData = {
                jobs: [{ id: 1, title: 'Test Job', company: 'Test Company' }],
                applications: [{ id: 1, job_title: 'Test Application' }],
                resumes: [{ id: 1, name: 'Test Resume' }]
            };
            
            let renderCount = 0;
            
            try {
                if (typeof window.displayJobsList === 'function') {
                    window.displayJobsList(mockData.jobs);
                    renderCount++;
                    log('Jobs rendering: SUCCESS', 'success');
                } else {
                    log('displayJobsList function not found', 'error');
                }
            } catch (error) {
                log(`Jobs rendering: ERROR - ${error.message}`, 'error');
            }
            
            updateResults('js-results', `Data Rendering: ${renderCount}/3 functions working`, renderCount > 0 ? 'success' : 'error');
        }

        async function testFullWorkflow() {
            log('Testing full workflow...', 'info');
            
            try {
                // Step 1: Login
                await testLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Step 2: Test each API
                await testDashboard();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testJobs();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testApplications();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testResumes();
                
                updateResults('integration-results', 'Full Workflow: COMPLETED', 'success');
                log('Full workflow test completed', 'success');
                
            } catch (error) {
                updateResults('integration-results', `Full Workflow: FAILED - ${error.message}`, 'error');
                log(`Full workflow failed: ${error.message}`, 'error');
            }
        }

        async function testTabNavigation() {
            log('Testing tab navigation...', 'info');
            
            const tabs = ['dashboard', 'jobs', 'applications', 'resumes'];
            let navCount = 0;
            
            for (const tab of tabs) {
                try {
                    // Simulate clicking on tab
                    const tabElement = document.querySelector(`[onclick*="show${tab.charAt(0).toUpperCase() + tab.slice(1)}"]`);
                    if (tabElement) {
                        tabElement.click();
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        navCount++;
                        log(`Tab ${tab}: NAVIGATION SUCCESS`, 'success');
                    } else {
                        log(`Tab ${tab}: ELEMENT NOT FOUND`, 'error');
                    }
                } catch (error) {
                    log(`Tab ${tab}: NAVIGATION ERROR - ${error.message}`, 'error');
                }
            }
            
            updateResults('integration-results', `Tab Navigation: ${navCount}/${tabs.length} successful`, navCount === tabs.length ? 'success' : 'warning');
        }

        // Auto-start basic tests
        window.onload = function() {
            log('Frontend Testing Suite loaded', 'success');
            log('Click buttons above to run specific tests', 'info');
        };
    </script>
</body>
</html>
