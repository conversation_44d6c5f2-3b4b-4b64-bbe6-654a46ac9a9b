#!/usr/bin/env python3
"""
Test live LinkedIn job search functionality
"""
import sys
import asyncio
import requests
import json
sys.path.append('.')

async def test_live_linkedin_job_search():
    """Test the live LinkedIn job search via API"""
    try:
        print("🔍 Testing Live LinkedIn Job Search...")
        
        # Test data
        search_data = {
            "keywords": "Python Developer",
            "location": "Remote",
            "job_type": "Full-time",
            "date_posted": "past_week"
        }
        
        print(f"   🔍 Search Keywords: {search_data['keywords']}")
        print(f"   📍 Location: {search_data['location']}")
        print(f"   💼 Job Type: {search_data['job_type']}")
        print(f"   📅 Date Posted: {search_data['date_posted']}")
        
        # Make API request to start job search
        print("\n   🚀 Starting job search via API...")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/jobs/search",
                json=search_data,
                headers={"Content-Type": "application/json"},
                timeout=300  # 5 minutes timeout
            )
            
            print(f"   📡 API Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ API Response: {result}")
                
                if "search_id" in result:
                    search_id = result["search_id"]
                    print(f"   🆔 Search ID: {search_id}")
                    
                    # Wait a moment for search to process
                    print("   ⏳ Waiting for search to complete...")
                    await asyncio.sleep(10)
                    
                    # Check search status
                    status_response = requests.get(
                        f"http://localhost:8000/api/jobs/search/{search_id}/status",
                        timeout=30
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"   📊 Search Status: {status_data}")
                    
                    return True
                else:
                    print(f"   ⚠️  No search_id in response: {result}")
                    return False
            else:
                print(f"   ❌ API Error: {response.status_code}")
                print(f"   📄 Response: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("   ⏰ Request timed out - LinkedIn search may be taking longer")
            return False
        except requests.exceptions.ConnectionError:
            print("   ❌ Connection error - server may not be running")
            return False
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_linkedin_bot():
    """Test LinkedIn bot directly"""
    try:
        print("\n🤖 Testing LinkedIn Bot Directly...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.api.jobs import JobSearchRequest
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Software Engineer",
            location="San Francisco",
            job_type="Full-time"
        )
        
        print(f"   🔍 Testing search: {search_request.keywords}")
        print("   🚀 Starting direct LinkedIn automation...")
        
        # Test job search
        try:
            jobs_found = await linkedin_bot.search_jobs(search_request)
            
            print(f"   📊 Jobs found: {jobs_found}")
            
            if jobs_found > 0:
                print("   ✅ Direct LinkedIn automation working!")
                return True
            else:
                print("   ⚠️  No jobs found - checking why...")
                
                # Check if login worked
                print("   🔍 Testing LinkedIn login separately...")
                login_success = await linkedin_bot.login()
                
                if login_success:
                    print("   ✅ LinkedIn login successful")
                    print("   💡 Issue might be with job extraction selectors")
                else:
                    print("   ❌ LinkedIn login failed")
                    print("   💡 Issue is with authentication")
                
                return False
                
        except Exception as e:
            print(f"   ❌ Direct bot test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # Clean up
            linkedin_bot.close()
            db.close()
        
    except Exception as e:
        print(f"   ❌ Direct bot test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_jobs():
    """Check current jobs in database"""
    try:
        print("\n💾 Checking Database for Existing Jobs...")
        
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.job import Job
        
        db = SessionLocal()
        
        # Check total jobs
        total_jobs = db.query(Job).count()
        print(f"   📊 Total jobs in database: {total_jobs}")
        
        if total_jobs > 0:
            # Show recent jobs
            recent_jobs = db.query(Job).order_by(Job.date_posted.desc()).limit(5).all()
            print("   📋 Recent jobs:")
            for i, job in enumerate(recent_jobs, 1):
                print(f"      {i}. {job.title} at {job.company}")
                print(f"         📍 {job.location or 'Location not specified'}")
                print(f"         📅 Posted: {job.date_posted.strftime('%Y-%m-%d %H:%M')}")
                print(f"         🔗 {job.url}")
                print()
        
        # Check users with LinkedIn credentials
        users_with_creds = db.query(User).filter(User.linkedin_email.isnot(None)).count()
        print(f"   👥 Users with LinkedIn credentials: {users_with_creds}")
        
        db.close()
        
    except Exception as e:
        print(f"   ❌ Database check failed: {e}")

def check_server_status():
    """Check if server is responding"""
    try:
        print("\n🌐 Checking Server Status...")
        
        # Test main page
        response = requests.get("http://localhost:8000/", timeout=10)
        print(f"   📡 Main page status: {response.status_code}")
        
        # Test API health
        try:
            api_response = requests.get("http://localhost:8000/docs", timeout=10)
            print(f"   📚 API docs status: {api_response.status_code}")
        except:
            print("   ⚠️  API docs not accessible")
        
        # Test jobs endpoint
        try:
            jobs_response = requests.get("http://localhost:8000/api/jobs", timeout=10)
            print(f"   💼 Jobs API status: {jobs_response.status_code}")
        except:
            print("   ⚠️  Jobs API not accessible")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Server check failed: {e}")
        return False

async def main():
    """Run comprehensive LinkedIn job search test"""
    print("🧪 Live LinkedIn Job Search Test")
    print("=" * 60)
    
    # Check server status
    server_ok = check_server_status()
    
    if not server_ok:
        print("\n❌ Server is not responding. Please start the server first.")
        return
    
    # Check database
    check_database_jobs()
    
    # Test API job search
    print("\n" + "=" * 60)
    api_test_ok = await test_live_linkedin_job_search()
    
    # Test direct bot
    print("\n" + "=" * 60)
    direct_test_ok = await test_direct_linkedin_bot()
    
    print("\n" + "=" * 60)
    print("📊 Final Test Results:")
    print(f"   Server Status: {'✅' if server_ok else '❌'}")
    print(f"   API Job Search: {'✅' if api_test_ok else '❌'}")
    print(f"   Direct Bot Test: {'✅' if direct_test_ok else '❌'}")
    
    if api_test_ok or direct_test_ok:
        print("\n🎉 SUCCESS! LinkedIn automation is working!")
        print("   💡 Live LinkedIn data should be available in the web interface")
    else:
        print("\n⚠️  LinkedIn automation needs attention:")
        if not api_test_ok:
            print("   🔧 API job search is not working")
        if not direct_test_ok:
            print("   🔧 Direct LinkedIn bot has issues")
        print("\n💡 Common solutions:")
        print("   1. Check LinkedIn credentials in web interface")
        print("   2. Complete 2FA if prompted")
        print("   3. Check internet connection")
        print("   4. LinkedIn may have changed their page structure")

if __name__ == "__main__":
    asyncio.run(main())
