/**
 * Jobs management functionality
 */

let currentJobsPage = 1;
let jobsPerPage = 20;
let currentJobsFilters = {};

/**
 * Load jobs data
 */
async function loadJobsData() {
    try {
        console.log('Loading jobs data...');
        showLoading('jobs-view');

        // Create jobs view if it doesn't exist
        const jobsView = document.getElementById('jobs-view');
        const jobsList = document.getElementById('jobs-list');
        console.log('Jobs view element:', jobsView);
        console.log('Jobs list element:', jobsList);

        if (!jobsList) {
            console.log('Creating jobs view (jobs-list not found)...');
            createJobsView();
            console.log('Jobs view created');
        } else {
            console.log('Jobs view already exists with proper structure');
        }

        console.log('Loading jobs list...');
        await loadJobsList();

    } catch (error) {
        console.error('Failed to load jobs data:', error);
        showNotification('Failed to load jobs data', 'error');
    } finally {
        hideLoading('jobs-view');
    }
}

/**
 * Create jobs view HTML
 */
function createJobsView() {
    const jobsView = document.getElementById('jobs-view');
    jobsView.innerHTML = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Jobs</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <button type="button" class="btn btn-sm btn-primary me-2" onclick="showJobSearchForm()">
                    <i class="fas fa-search me-1"></i>Search Jobs
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshJobs()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="job-filter-keywords" placeholder="Keywords">
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="job-filter-company" placeholder="Company">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="job-filter-applied">
                            <option value="">All Jobs</option>
                            <option value="false">Not Applied</option>
                            <option value="true">Applied</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="job-filter-bookmarked">
                            <option value="">All</option>
                            <option value="true">Bookmarked</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary w-100" onclick="applyJobsFilters()">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jobs List -->
        <div id="jobs-list">
            <!-- Jobs will be loaded here -->
        </div>

        <!-- Pagination -->
        <div id="jobs-pagination" class="d-flex justify-content-center mt-4">
            <!-- Pagination will be loaded here -->
        </div>
    `;
}

/**
 * Load jobs list
 */
async function loadJobsList() {
    try {
        const params = new URLSearchParams({
            page: currentJobsPage,
            per_page: jobsPerPage,
            ...currentJobsFilters
        });

        console.log('Making API call to:', `/jobs/?${params}`);
        const response = await apiCall(`/jobs/?${params}`);
        console.log('API response:', response);

        if (response) {
            console.log('Displaying jobs:', response.jobs?.length || 0, 'jobs');
            displayJobsList(response.jobs);
            displayJobsPagination(response);
        } else {
            console.log('No response from API');
        }

    } catch (error) {
        console.error('Failed to load jobs list:', error);
        showNotification('Failed to load jobs', 'error');
    }
}

/**
 * Display jobs list
 */
function displayJobsList(jobs) {
    console.log('displayJobsList called with:', jobs);
    const container = document.getElementById('jobs-list');
    console.log('Container element:', container);

    if (!jobs || jobs.length === 0) {
        console.log('No jobs to display');
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No jobs found</h4>
                <p class="text-muted">Try adjusting your search criteria or start a new job search.</p>
                <button class="btn btn-primary" onclick="showJobSearchForm()">
                    <i class="fas fa-search me-1"></i>Search for Jobs
                </button>
            </div>
        `;
        return;
    }

    console.log('Creating job cards for', jobs.length, 'jobs');
    container.innerHTML = jobs.map(job => createJobCard(job)).join('');
    console.log('Jobs displayed successfully');
}

/**
 * Create job card HTML
 */
function createJobCard(job) {
    try {
        console.log('Creating job card for:', job);
        const matchScore = job.match_score ? Math.round(job.match_score * 100) : null;
        const matchScoreClass = matchScore >= 80 ? 'high' : matchScore >= 60 ? 'medium' : 'low';

        const cardHTML = `
            <div class="job-card" data-job-id="${job.id}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h5 class="job-title mb-1">
                            <a href="${job.url || '#'}" target="_blank" rel="noopener">${job.title || 'No Title'}</a>
                        </h5>
                        <div class="job-company">${job.company || 'No Company'}</div>
                        ${job.location ? `<div class="job-location"><i class="fas fa-map-marker-alt me-1"></i>${job.location}</div>` : ''}
                    </div>
                    <div class="job-actions">
                        <button class="btn btn-sm btn-outline-secondary me-1" onclick="toggleBookmark(${job.id})" title="Bookmark">
                            <i class="fas fa-bookmark ${job.is_bookmarked ? 'text-warning' : ''}"></i>
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="viewJobDetails(${job.id})">
                                    <i class="fas fa-eye me-2"></i>View Details
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="applyToJob(${job.id})">
                                    <i class="fas fa-paper-plane me-2"></i>Apply
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteJob(${job.id})">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="job-meta">
                    ${job.easy_apply ? '<span class="job-badge badge-easy-apply">Easy Apply</span>' : ''}
                    ${job.is_applied ? '<span class="job-badge badge-applied">Applied</span>' : ''}
                    ${job.is_bookmarked ? '<span class="job-badge badge-bookmarked">Bookmarked</span>' : ''}
                    ${job.job_type ? `<span class="text-muted">${job.job_type}</span>` : ''}
                    ${job.experience_level ? `<span class="text-muted">${job.experience_level}</span>` : ''}
                </div>

                ${matchScore ? `
                    <div class="match-score mt-2">
                        <span class="text-muted me-2">Match Score:</span>
                        <div class="match-score-bar">
                            <div class="match-score-fill ${matchScoreClass}" style="width: ${matchScore}%"></div>
                        </div>
                        <span class="text-muted">${matchScore}%</span>
                    </div>
                ` : ''}

                <div class="text-muted small mt-2">
                    Posted: ${formatRelativeTime(job.created_at || job.date_posted)}
                </div>
            </div>
        `;

        console.log('Job card HTML created successfully');
        return cardHTML;
    } catch (error) {
        console.error('Error creating job card:', error, job);
        return `<div class="alert alert-danger">Error displaying job: ${job.title || 'Unknown'}</div>`;
    }
}

/**
 * Display pagination
 */
function displayJobsPagination(response) {
    const container = document.getElementById('jobs-pagination');
    
    if (response.total <= jobsPerPage) {
        container.innerHTML = '';
        return;
    }
    
    const totalPages = Math.ceil(response.total / jobsPerPage);
    const currentPage = response.page;
    
    let paginationHTML = '<nav><ul class="pagination">';
    
    // Previous button
    if (response.has_prev) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="goToJobsPage(${currentPage - 1})">Previous</a>
        </li>`;
    }
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<li class="page-item ${i === currentPage ? 'active' : ''}">
            <a class="page-link" href="#" onclick="goToJobsPage(${i})">${i}</a>
        </li>`;
    }
    
    // Next button
    if (response.has_next) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="goToJobsPage(${currentPage + 1})">Next</a>
        </li>`;
    }
    
    paginationHTML += '</ul></nav>';
    container.innerHTML = paginationHTML;
}

/**
 * Go to specific page
 */
async function goToJobsPage(page) {
    currentJobsPage = page;
    await loadJobsList();
}

/**
 * Apply filters
 */
async function applyJobsFilters() {
    currentJobsFilters = {
        keywords: document.getElementById('job-filter-keywords').value,
        company: document.getElementById('job-filter-company').value,
        is_applied: document.getElementById('job-filter-applied').value,
        is_bookmarked: document.getElementById('job-filter-bookmarked').value
    };
    
    // Remove empty filters
    Object.keys(currentJobsFilters).forEach(key => {
        if (!currentJobsFilters[key]) {
            delete currentJobsFilters[key];
        }
    });
    
    currentJobsPage = 1;
    await loadJobsList();
}

/**
 * Toggle bookmark status
 */
async function toggleBookmark(jobId) {
    try {
        const response = await apiCall(`/jobs/${jobId}/bookmark`, 'POST');
        
        if (response) {
            showNotification(response.message, 'success');
            
            // Update the bookmark icon
            const jobCard = document.querySelector(`[data-job-id="${jobId}"]`);
            const bookmarkIcon = jobCard.querySelector('.fa-bookmark');
            
            if (response.is_bookmarked) {
                bookmarkIcon.classList.add('text-warning');
            } else {
                bookmarkIcon.classList.remove('text-warning');
            }
        }
        
    } catch (error) {
        showNotification('Failed to update bookmark', 'error');
    }
}

/**
 * View job details
 */
async function viewJobDetails(jobId) {
    try {
        const job = await apiCall(`/jobs/${jobId}`);
        
        if (job) {
            showJobDetailsModal(job);
        }
        
    } catch (error) {
        showNotification('Failed to load job details', 'error');
    }
}

/**
 * Show job details modal
 */
function showJobDetailsModal(job) {
    // Create modal HTML
    const modalHTML = `
        <div class="modal fade" id="jobDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${job.title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <h6>Company</h6>
                            <p>${job.company}</p>
                        </div>
                        ${job.location ? `
                            <div class="mb-3">
                                <h6>Location</h6>
                                <p><i class="fas fa-map-marker-alt me-1"></i>${job.location}</p>
                            </div>
                        ` : ''}
                        ${job.description ? `
                            <div class="mb-3">
                                <h6>Description</h6>
                                <div class="job-description">${job.description}</div>
                            </div>
                        ` : ''}
                        <div class="mb-3">
                            <h6>Job Details</h6>
                            <ul class="list-unstyled">
                                ${job.job_type ? `<li><strong>Type:</strong> ${job.job_type}</li>` : ''}
                                ${job.experience_level ? `<li><strong>Experience:</strong> ${job.experience_level}</li>` : ''}
                                ${job.easy_apply ? '<li><strong>Easy Apply:</strong> Yes</li>' : ''}
                                <li><strong>Posted:</strong> ${formatDate(job.created_at)}</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="${job.url}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-1"></i>View on LinkedIn
                        </a>
                        <button type="button" class="btn btn-success" onclick="applyToJob(${job.id}); bootstrap.Modal.getInstance(document.getElementById('jobDetailsModal')).hide();">
                            <i class="fas fa-paper-plane me-1"></i>Apply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal
    const existingModal = document.getElementById('jobDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('jobDetailsModal'));
    modal.show();
}

/**
 * Apply to job
 */
async function applyToJob(jobId) {
    // This would open the application form
    // For now, redirect to applications page
    showNotification('Application feature coming soon!', 'info');
    // showApplicationForm(jobId);
}

/**
 * Delete job
 */
async function deleteJob(jobId) {
    if (!confirm('Are you sure you want to delete this job?')) {
        return;
    }
    
    try {
        await apiCall(`/jobs/${jobId}`, 'DELETE');
        showNotification('Job deleted successfully', 'success');
        await loadJobsList();
        
    } catch (error) {
        showNotification('Failed to delete job', 'error');
    }
}

/**
 * Refresh jobs
 */
async function refreshJobs() {
    const refreshButton = document.querySelector('[onclick="refreshJobs()"]');
    const originalContent = refreshButton.innerHTML;
    
    try {
        refreshButton.innerHTML = '<i class="fas fa-sync-alt fa-spin me-1"></i>Refreshing...';
        refreshButton.disabled = true;
        
        await loadJobsList();
        showNotification('Jobs refreshed successfully', 'success');
        
    } catch (error) {
        showNotification('Failed to refresh jobs', 'error');
    } finally {
        refreshButton.innerHTML = originalContent;
        refreshButton.disabled = false;
    }
}

/**
 * Show job search form
 */
function showJobSearchForm() {
    showJobSearch();
}
