/**
 * Dashboard functionality for LinkedIn Automation Bot POC
 */

let jobTrendsChart = null;

/**
 * Load dashboard data
 */
async function loadDashboardData() {
    try {
        console.log('Loading dashboard data...');
        showLoading('dashboard-view');

        const dashboardData = await apiCall('/dashboard/');
        console.log('Dashboard API response:', dashboardData);

        if (dashboardData) {
            console.log('Updating dashboard components...');
            updateDashboardStats(dashboardData.stats);
            updateRecentActivity(dashboardData.recent_activity);
            updateJobTrendsChart(dashboardData.job_trends);
            updateTopCompanies(dashboardData.top_companies);
            updatePendingReviews(dashboardData.pending_reviews);
            console.log('Dashboard updated successfully');
        } else {
            console.log('No dashboard data received');
        }

    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        showNotification('Failed to load dashboard data', 'error');
    } finally {
        hideLoading('dashboard-view');
    }
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats(stats) {
    if (!stats) return;
    
    document.getElementById('total-jobs').textContent = stats.total_jobs || 0;
    document.getElementById('total-applications').textContent = stats.total_applications || 0;
    document.getElementById('success-rate').textContent = (stats.success_rate || 0) + '%';
    document.getElementById('pending-reviews').textContent = stats.pending_applications || 0;
}

/**
 * Update recent activity list
 */
function updateRecentActivity(activities) {
    const container = document.getElementById('recent-activity');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-2x mb-3"></i>
                <p>No recent activity</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon ${activity.type}">
                <i class="fas ${getActivityIcon(activity.type)}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-description">${activity.description}</div>
                <div class="activity-time">${formatRelativeTime(activity.timestamp)}</div>
            </div>
        </div>
    `).join('');
}

/**
 * Get icon for activity type
 */
function getActivityIcon(type) {
    const icons = {
        'job_found': 'fa-briefcase',
        'application_created': 'fa-paper-plane',
        'status_updated': 'fa-sync-alt'
    };
    return icons[type] || 'fa-info-circle';
}

/**
 * Update job trends chart
 */
function updateJobTrendsChart(trends) {
    const ctx = document.getElementById('jobTrendsChart');
    
    if (!trends || trends.length === 0) {
        ctx.parentElement.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-chart-line fa-2x mb-3"></i>
                <p>No trend data available</p>
            </div>
        `;
        return;
    }
    
    // Destroy existing chart if it exists
    if (jobTrendsChart) {
        jobTrendsChart.destroy();
    }
    
    const labels = trends.map(trend => {
        const date = new Date(trend.date);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });
    
    const jobsData = trends.map(trend => trend.jobs_found);
    const applicationsData = trends.map(trend => trend.applications_submitted);
    
    jobTrendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Jobs Found',
                    data: jobsData,
                    borderColor: '#0077b5',
                    backgroundColor: 'rgba(0, 119, 181, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Applications Submitted',
                    data: applicationsData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

/**
 * Update top companies section
 */
function updateTopCompanies(companies) {
    // This would be implemented if we had a top companies section in the dashboard
    console.log('Top companies:', companies);
}

/**
 * Update pending reviews section
 */
function updatePendingReviews(pendingReviews) {
    if (!pendingReviews || pendingReviews.length === 0) {
        return;
    }
    
    // Show notification for pending reviews
    const count = pendingReviews.length;
    showNotification(
        `You have ${count} application${count > 1 ? 's' : ''} that require manual review.`,
        'warning',
        0 // Don't auto-dismiss
    );
}

/**
 * Refresh dashboard data
 */
async function refreshDashboard() {
    const refreshButton = document.querySelector('[onclick="refreshDashboard()"]');
    const originalContent = refreshButton.innerHTML;
    
    try {
        refreshButton.innerHTML = '<i class="fas fa-sync-alt fa-spin me-1"></i>Refreshing...';
        refreshButton.disabled = true;
        
        await loadDashboardData();
        
        showNotification('Dashboard refreshed successfully', 'success');
        
    } catch (error) {
        showNotification('Failed to refresh dashboard', 'error');
    } finally {
        refreshButton.innerHTML = originalContent;
        refreshButton.disabled = false;
    }
}

/**
 * Initialize dashboard when view is shown
 */
function initializeDashboard() {
    // Set up chart container height
    const chartContainer = document.getElementById('jobTrendsChart').parentElement;
    chartContainer.style.height = '300px';
    
    // Load initial data
    loadDashboardData();
}

/**
 * Dashboard quick actions
 */
function quickStartJobSearch() {
    showJobSearch();
    // Focus on the keywords input
    setTimeout(() => {
        const keywordsInput = document.getElementById('job-keywords');
        if (keywordsInput) {
            keywordsInput.focus();
        }
    }, 100);
}

function quickViewApplications() {
    showApplications();
}

function quickUploadResume() {
    showResumes();
    // Trigger file upload
    setTimeout(() => {
        const uploadButton = document.getElementById('upload-resume-btn');
        if (uploadButton) {
            uploadButton.click();
        }
    }, 100);
}

/**
 * Dashboard widgets
 */
function createStatsWidget(title, value, icon, color, trend = null) {
    const trendHtml = trend ? `
        <div class="stats-trend ${trend.direction}">
            <i class="fas fa-arrow-${trend.direction === 'up' ? 'up' : 'down'}"></i>
            ${trend.value}%
        </div>
    ` : '';
    
    return `
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-${color} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-${color} text-uppercase mb-1">
                                ${title}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${value}
                            </div>
                            ${trendHtml}
                        </div>
                        <div class="col-auto">
                            <i class="fas ${icon} fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Real-time updates for dashboard
 */
function startDashboardUpdates() {
    // Update every 30 seconds when dashboard is active
    const updateInterval = setInterval(async () => {
        if (currentView === 'dashboard') {
            try {
                const stats = await apiCall('/dashboard/');
                if (stats) {
                    updateDashboardStats(stats.stats);
                }
            } catch (error) {
                console.error('Failed to update dashboard stats:', error);
            }
        } else {
            clearInterval(updateInterval);
        }
    }, 30000);
}

/**
 * Export dashboard data
 */
async function exportDashboardData() {
    try {
        const data = await apiCall('/dashboard/export');
        
        // Create and download CSV
        const csv = convertToCSV(data);
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `dashboard_data_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showNotification('Dashboard data exported successfully', 'success');
        
    } catch (error) {
        showNotification('Failed to export dashboard data', 'error');
    }
}

/**
 * Convert data to CSV format
 */
function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    return csvContent;
}
