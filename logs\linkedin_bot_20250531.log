2025-05-31 01:39:05 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 01:39:05 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 01:39:05 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 09:54:44 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 09:54:44 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 09:56:26 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-31 09:56:27 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 09:56:31 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 09:57:40 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 09:58:12 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 09:58:29 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 09:58:29 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 09:58:29 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_3_1748707109
2025-05-31 09:58:29 - app.api.jobs - ERROR - perform_job_search:290 - Background job search failed: Client.__init__() got an unexpected keyword argument 'proxies'
2025-05-31 10:04:02 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:04:07 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:04:07 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:04:07 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:04:28 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:04:31 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:04:31 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:04:31 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:14:03 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:14:07 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:14:07 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:14:07 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:15:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:15:07 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:15:07 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:15:07 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:16:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:16:15 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:16:15 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:16:15 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:18:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:18:17 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:18:17 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:18:17 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:24:26 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-31 10:24:26 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 10:25:00 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 10:25:52 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 10:25:52 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 10:25:52 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748708752
2025-05-31 10:25:52 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-05-31 10:25:52 - app.services.linkedin_bot - INFO - __init__:41 - AI service initialized for LinkedIn bot
2025-05-31 10:25:52 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-05-31 10:25:52 - WDM - INFO - log:11 - ====== WebDriver manager ======
2025-05-31 10:25:53 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-05-31 10:25:54 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-05-31 10:25:54 - WDM - INFO - log:11 - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-05-31 10:25:54 - app.services.linkedin_bot - ERROR - initialize_driver:68 - Failed to initialize WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-05-31 10:25:54 - app.services.linkedin_bot - ERROR - search_jobs:179 - Job search failed: [WinError 193] %1 is not a valid Win32 application
2025-05-31 10:25:54 - app.api.jobs - INFO - perform_job_search:278 - LinkedIn job search completed: 0 jobs found for search search_1_1748708752
2025-05-31 10:26:03 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 10:33:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:33:15 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:33:15 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:33:15 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:34:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:34:12 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:34:12 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:34:12 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:35:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:35:08 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:35:08 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:35:08 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:37:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:55:36 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:55:36 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:55:36 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:55:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 10:55:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 10:55:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 10:55:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 10:55:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 10:55:36 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 10:55:37 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 12:22:36 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-31 12:22:37 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 12:22:45 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 12:23:00 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 12:23:00 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 12:23:00 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748715780
2025-05-31 12:23:00 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-05-31 12:23:00 - app.services.linkedin_bot - INFO - __init__:42 - AI service initialized for LinkedIn bot
2025-05-31 12:23:00 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-05-31 12:23:00 - app.services.linkedin_bot - INFO - initialize_driver:50 - Initializing Chrome WebDriver...
2025-05-31 12:23:00 - app.services.linkedin_bot - INFO - initialize_driver:58 - Running in headless mode
2025-05-31 12:23:00 - app.services.linkedin_bot - INFO - initialize_driver:76 - Attempting ChromeDriverManager approach...
2025-05-31 12:23:00 - app.services.linkedin_bot - WARNING - initialize_driver:98 - ChromeDriverManager approach failed: cannot import name 'ChromeType' from 'webdriver_manager.core.utils' (C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\webdriver_manager\core\utils.py)
2025-05-31 12:23:00 - app.services.linkedin_bot - INFO - initialize_driver:103 - Attempting system Chrome approach...
2025-05-31 12:23:02 - app.services.linkedin_bot - INFO - initialize_driver:106 - System Chrome approach successful
2025-05-31 12:23:02 - app.services.linkedin_bot - INFO - initialize_driver:144 - Testing WebDriver with basic navigation...
2025-05-31 12:23:02 - app.services.linkedin_bot - INFO - initialize_driver:148 - Chrome WebDriver initialized and tested successfully
2025-05-31 12:23:02 - app.services.linkedin_bot - INFO - login:179 - Attempting LinkedIn login <NAME_EMAIL>
2025-05-31 12:23:15 - app.services.linkedin_bot - WARNING - login:213 - LinkedIn 2FA challenge detected. Manual intervention required.
2025-05-31 12:23:45 - app.services.linkedin_bot - ERROR - login:222 - LinkedIn login failed
2025-05-31 12:23:47 - app.api.jobs - INFO - perform_job_search:278 - LinkedIn job search completed: 0 jobs found for search search_1_1748715780
2025-05-31 12:23:51 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776CE80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/50d70f6a650e2898f0d90d1db9382052
2025-05-31 12:23:55 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776E560>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/50d70f6a650e2898f0d90d1db9382052
2025-05-31 12:23:59 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776CD90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/50d70f6a650e2898f0d90d1db9382052
2025-05-31 12:24:27 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 12:26:26 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 12:26:38 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 12:26:38 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 12:26:38 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748715998
2025-05-31 12:26:38 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-05-31 12:26:38 - app.services.linkedin_bot - INFO - __init__:42 - AI service initialized for LinkedIn bot
2025-05-31 12:26:38 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-05-31 12:26:38 - app.services.linkedin_bot - INFO - initialize_driver:50 - Initializing Chrome WebDriver...
2025-05-31 12:26:38 - app.services.linkedin_bot - INFO - initialize_driver:58 - Running in headless mode
2025-05-31 12:26:38 - app.services.linkedin_bot - INFO - initialize_driver:76 - Attempting ChromeDriverManager approach...
2025-05-31 12:26:38 - app.services.linkedin_bot - WARNING - initialize_driver:98 - ChromeDriverManager approach failed: cannot import name 'ChromeType' from 'webdriver_manager.core.utils' (C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\webdriver_manager\core\utils.py)
2025-05-31 12:26:38 - app.services.linkedin_bot - INFO - initialize_driver:103 - Attempting system Chrome approach...
2025-05-31 12:26:40 - app.services.linkedin_bot - INFO - initialize_driver:106 - System Chrome approach successful
2025-05-31 12:26:40 - app.services.linkedin_bot - INFO - initialize_driver:144 - Testing WebDriver with basic navigation...
2025-05-31 12:26:40 - app.services.linkedin_bot - INFO - initialize_driver:148 - Chrome WebDriver initialized and tested successfully
2025-05-31 12:26:40 - app.services.linkedin_bot - INFO - login:179 - Attempting LinkedIn login <NAME_EMAIL>
2025-05-31 12:26:51 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776E950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053/element
2025-05-31 12:26:55 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA7213340>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053/element
2025-05-31 12:26:59 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA770EEC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053/element
2025-05-31 12:27:03 - app.services.linkedin_bot - ERROR - login:226 - LinkedIn login error: HTTPConnectionPool(host='localhost', port=59183): Max retries exceeded with url: /session/45899448facb8901507d22a05a377053/element (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA721FC70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-05-31 12:27:07 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776EFE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053
2025-05-31 12:27:11 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776D6C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053
2025-05-31 12:27:15 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776D180>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053
2025-05-31 12:27:24 - app.api.jobs - INFO - perform_job_search:278 - LinkedIn job search completed: 0 jobs found for search search_1_1748715998
2025-05-31 12:27:28 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776E6B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053
2025-05-31 12:27:32 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776D270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053
2025-05-31 12:27:36 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025EA776EC80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/45899448facb8901507d22a05a377053
2025-05-31 12:27:44 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 12:27:54 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 12:27:54 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 12:27:54 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 12:28:34 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 12:56:13 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 12:56:13 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 12:58:41 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-31 12:58:41 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 12:58:49 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 12:59:02 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 12:59:02 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 12:59:02 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748717942
2025-05-31 12:59:02 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-05-31 12:59:02 - app.services.linkedin_bot - INFO - __init__:42 - AI service initialized for LinkedIn bot
2025-05-31 12:59:02 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-05-31 12:59:02 - app.services.linkedin_bot - INFO - initialize_driver:50 - Initializing Chrome WebDriver...
2025-05-31 12:59:02 - app.services.linkedin_bot - INFO - initialize_driver:58 - Running in headless mode
2025-05-31 12:59:02 - app.services.linkedin_bot - INFO - initialize_driver:76 - Attempting ChromeDriverManager approach...
2025-05-31 12:59:02 - app.services.linkedin_bot - WARNING - initialize_driver:97 - ChromeDriverManager approach failed: ChromeDriverManager.__init__() got an unexpected keyword argument 'cache_valid_range'
2025-05-31 12:59:02 - app.services.linkedin_bot - INFO - initialize_driver:102 - Attempting system Chrome approach...
2025-05-31 12:59:06 - app.services.linkedin_bot - INFO - initialize_driver:105 - System Chrome approach successful
2025-05-31 12:59:06 - app.services.linkedin_bot - INFO - initialize_driver:143 - Testing WebDriver with basic navigation...
2025-05-31 12:59:06 - app.services.linkedin_bot - INFO - initialize_driver:147 - Chrome WebDriver initialized and tested successfully
2025-05-31 12:59:06 - app.services.linkedin_bot - INFO - login:178 - Attempting LinkedIn login <NAME_EMAIL>
2025-05-31 12:59:25 - app.services.linkedin_bot - INFO - login:207 - LinkedIn login successful
2025-05-31 12:59:25 - app.services.linkedin_bot - INFO - search_jobs:237 - Starting job search: Devops engineer
2025-05-31 13:00:06 - app.services.linkedin_bot - ERROR - search_jobs:273 - Job search failed: Message: 
Stacktrace:
	GetHandleVerifier [0x00007FF6E64ECF45+75717]
	GetHandleVerifier [0x00007FF6E64ECFA0+75808]
	(No symbol) [0x00007FF6E62B8F9A]
	(No symbol) [0x00007FF6E630F4C6]
	(No symbol) [0x00007FF6E630F77C]
	(No symbol) [0x00007FF6E6362577]
	(No symbol) [0x00007FF6E63373BF]
	(No symbol) [0x00007FF6E635F39C]
	(No symbol) [0x00007FF6E6337153]
	(No symbol) [0x00007FF6E6300421]
	(No symbol) [0x00007FF6E63011B3]
	GetHandleVerifier [0x00007FF6E67ED71D+3223453]
	GetHandleVerifier [0x00007FF6E67E7CC2+3200322]
	GetHandleVerifier [0x00007FF6E6805AF3+3322739]
	GetHandleVerifier [0x00007FF6E6506A1A+180890]
	GetHandleVerifier [0x00007FF6E650E11F+211359]
	GetHandleVerifier [0x00007FF6E64F5294+109332]
	GetHandleVerifier [0x00007FF6E64F5442+109762]
	GetHandleVerifier [0x00007FF6E64DBA59+4825]
	BaseThreadInitThunk [0x00007FF94018E8D7+23]
	RtlUserThreadStart [0x00007FF94243C5DC+44]

2025-05-31 13:00:09 - app.api.jobs - INFO - perform_job_search:278 - LinkedIn job search completed: 0 jobs found for search search_1_1748717942
2025-05-31 13:00:13 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000146973F8AF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e140fd560dcee095cfc53275afaa2fd4
2025-05-31 13:00:17 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000146973F9CC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e140fd560dcee095cfc53275afaa2fd4
2025-05-31 13:00:21 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000146973F8D30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e140fd560dcee095cfc53275afaa2fd4
