2025-05-31 01:39:05 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 01:39:05 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 01:39:05 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 09:54:44 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 09:54:44 - app.core.security - ERROR - verify_token:105 - Token verification failed: Signature has expired.
2025-05-31 09:56:26 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-31 09:56:27 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 09:56:31 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 09:57:40 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 09:58:12 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 09:58:29 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 09:58:29 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 09:58:29 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_3_1748707109
2025-05-31 09:58:29 - app.api.jobs - ERROR - perform_job_search:290 - Background job search failed: Client.__init__() got an unexpected keyword argument 'proxies'
2025-05-31 10:04:02 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:04:07 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:04:07 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:04:07 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:04:28 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:04:31 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:04:31 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:04:31 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:14:03 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:14:07 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:14:07 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:14:07 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:15:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:15:07 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:15:07 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:15:07 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:16:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:16:15 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:16:15 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:16:15 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:18:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:18:17 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:18:17 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:18:17 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:24:26 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-31 10:24:26 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-05-31 10:25:00 - app.api.auth - INFO - save_linkedin_credentials:193 - LinkedIn credentials saved for user: <EMAIL>
2025-05-31 10:25:52 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-05-31 10:25:52 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops engineer
2025-05-31 10:25:52 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748708752
2025-05-31 10:25:52 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-05-31 10:25:52 - app.services.linkedin_bot - INFO - __init__:41 - AI service initialized for LinkedIn bot
2025-05-31 10:25:52 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-05-31 10:25:52 - WDM - INFO - log:11 - ====== WebDriver manager ======
2025-05-31 10:25:53 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-05-31 10:25:54 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-05-31 10:25:54 - WDM - INFO - log:11 - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-05-31 10:25:54 - app.services.linkedin_bot - ERROR - initialize_driver:68 - Failed to initialize WebDriver: [WinError 193] %1 is not a valid Win32 application
2025-05-31 10:25:54 - app.services.linkedin_bot - ERROR - search_jobs:179 - Job search failed: [WinError 193] %1 is not a valid Win32 application
2025-05-31 10:25:54 - app.api.jobs - INFO - perform_job_search:278 - LinkedIn job search completed: 0 jobs found for search search_1_1748708752
2025-05-31 10:26:03 - app.api.resumes - INFO - get_resumes:51 - Retrieved 2 resumes <NAME_EMAIL>
2025-05-31 10:33:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:33:15 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:33:15 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:33:15 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:34:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:34:12 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:34:12 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:34:12 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:35:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-05-31 10:35:08 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-05-31 10:35:08 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-05-31 10:35:08 - app.main - INFO - lifespan:29 - Database initialized
2025-05-31 10:37:04 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
