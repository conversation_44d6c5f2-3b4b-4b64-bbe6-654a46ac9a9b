#!/usr/bin/env python3
"""
Test the improved WebDriver initialization
"""
import sys
import asyncio
import os
sys.path.append('.')

async def test_webdriver_initialization():
    """Test the improved WebDriver initialization"""
    try:
        print("🔍 Testing Improved WebDriver Initialization...")
        
        # Import the LinkedIn bot service
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        
        print("   ✅ Imports successful")
        
        # Get database session and user
        db = SessionLocal()
        user = db.query(User).first()
        
        if not user:
            print("   ❌ No users found in database")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot instance
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot instance created")
        
        # Test WebDriver initialization
        print("   🧪 Testing WebDriver initialization...")
        
        try:
            await linkedin_bot.initialize_driver()
            print("   ✅ WebDriver initialized successfully!")
            
            # Test if driver is working
            if linkedin_bot.driver:
                print("   ✅ WebDriver is active and ready")
                
                # Test basic navigation
                print("   🧪 Testing navigation...")
                linkedin_bot.driver.get("https://www.example.com")
                
                if "Example" in linkedin_bot.driver.title:
                    print("   ✅ Navigation test successful")
                else:
                    print(f"   ⚠️  Navigation test partial - Title: {linkedin_bot.driver.title}")
                
            else:
                print("   ❌ WebDriver is None after initialization")
                return False
                
        except Exception as e:
            print(f"   ❌ WebDriver initialization failed: {e}")
            return False
        
        finally:
            # Clean up
            if linkedin_bot.driver:
                linkedin_bot.driver.quit()
                print("   ✅ WebDriver closed successfully")
            
            db.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_chrome_installation():
    """Check if Chrome is installed"""
    print("🔍 Checking Chrome Installation...")
    
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.environ.get('USERNAME', '')),
    ]
    
    chrome_found = False
    for chrome_path in chrome_paths:
        if os.path.exists(chrome_path):
            print(f"   ✅ Chrome found at: {chrome_path}")
            chrome_found = True
            break
    
    if not chrome_found:
        print("   ❌ Chrome not found in standard locations")
        print("   💡 Please install Google Chrome browser")
        return False
    
    return True

async def test_linkedin_bot_with_real_search():
    """Test LinkedIn bot with a real search attempt"""
    try:
        print("\n🔗 Testing LinkedIn Bot with Real Search...")
        
        from app.api.jobs import perform_job_search, JobSearchRequest
        from app.core.database import SessionLocal
        from app.models.user import User
        
        # Get user with LinkedIn credentials
        db = SessionLocal()
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ⚠️  No users with LinkedIn credentials found")
            print("   💡 Add LinkedIn credentials in the web app Settings")
            return True  # Not a failure, just no credentials
            
        print(f"   ✅ Found user with LinkedIn credentials: {user.email}")
        
        # Create search request
        search_request = JobSearchRequest(
            keywords="Software Engineer",
            location="Remote",
            job_type="Full-time"
        )
        
        print("   ✅ Created search request")
        
        # Test the job search
        print("   🧪 Testing job search with real LinkedIn automation...")
        
        await perform_job_search(
            "test_real_search_123",
            search_request,
            user.id,
            db
        )
        
        print("   ✅ Job search completed (check logs for details)")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Real search test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 WebDriver Fix Test")
    print("=" * 50)
    
    # Check Chrome installation first
    chrome_ok = check_chrome_installation()
    
    if not chrome_ok:
        print("\n❌ Chrome not found. Please install Google Chrome browser.")
        print("   Download from: https://www.google.com/chrome/")
        return
    
    # Test WebDriver initialization
    webdriver_ok = await test_webdriver_initialization()
    
    # Test real LinkedIn search if WebDriver works
    if webdriver_ok:
        search_ok = await test_linkedin_bot_with_real_search()
    else:
        search_ok = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Chrome Installation: {'✅' if chrome_ok else '❌'}")
    print(f"   WebDriver Initialization: {'✅' if webdriver_ok else '❌'}")
    print(f"   LinkedIn Search Test: {'✅' if search_ok else '❌'}")
    
    if chrome_ok and webdriver_ok and search_ok:
        print("\n🎉 All tests passed! LinkedIn automation should work now.")
        print("\n🎯 Next steps:")
        print("   1. Make sure you have LinkedIn credentials in Settings")
        print("   2. Try searching for jobs in the web interface")
        print("   3. You should see real LinkedIn jobs (not just mock data)")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        print("\n🔧 Troubleshooting:")
        if not chrome_ok:
            print("   - Install Google Chrome browser")
        if not webdriver_ok:
            print("   - Check Chrome version compatibility")
            print("   - Try running as administrator")
        if not search_ok:
            print("   - Add LinkedIn credentials in Settings")
            print("   - Check internet connection")

if __name__ == "__main__":
    asyncio.run(main())
