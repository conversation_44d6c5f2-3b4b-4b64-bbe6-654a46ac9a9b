/**
 * Main JavaScript file for LinkedIn Automation Bot POC
 */

// Global variables
let currentUser = null;
let authToken = null;
let currentView = 'dashboard';

// API Base URL
const API_BASE = '/api';

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
async function initializeApp() {
    try {
        // Check if user is logged in
        authToken = localStorage.getItem('authToken');
        
        if (!authToken) {
            showLoginForm();
            return;
        }
        
        // Verify token and get user info
        const userInfo = await apiCall('/auth/me', 'GET');
        if (userInfo) {
            currentUser = userInfo;
            document.getElementById('username').textContent = userInfo.username;
            showDashboard();
            startPeriodicUpdates();
        } else {
            showLoginForm();
        }
        
    } catch (error) {
        console.error('Failed to initialize app:', error);
        showLoginForm();
    }
}

/**
 * Make API calls with authentication
 */
async function apiCall(endpoint, method = 'GET', data = null, isFormData = false) {
    try {
        console.log(`Making API call: ${method} ${API_BASE + endpoint}`);
        const headers = {};

        if (authToken) {
            headers['Authorization'] = `Bearer ${authToken}`;
            console.log('Auth token present:', authToken ? 'Yes' : 'No');
        } else {
            console.log('No auth token available');
        }

        if (data && !isFormData) {
            headers['Content-Type'] = 'application/json';
        }

        const config = {
            method: method,
            headers: headers
        };

        if (data) {
            if (isFormData) {
                config.body = data;
            } else {
                config.body = JSON.stringify(data);
            }
        }

        const response = await fetch(API_BASE + endpoint, config);
        console.log(`API response status: ${response.status}`);

        if (response.status === 401) {
            // Token expired or invalid
            console.log('Authentication failed - logging out');
            logout();
            return null;
        }

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('API error response:', errorData);
            throw new Error(errorData.detail || `HTTP ${response.status}`);
        }

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            const result = await response.json();
            console.log('API response data:', result);
            return result;
        }

        const result = await response.text();
        console.log('API response text:', result);
        return result;

    } catch (error) {
        console.error('API call failed:', error);
        showNotification('API Error: ' + error.message, 'error');
        throw error;
    }
}

/**
 * Show login form
 */
function showLoginForm() {
    document.body.innerHTML = `
        <div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
            <div class="row w-100">
                <div class="col-md-6 col-lg-4 mx-auto">
                    <div class="card shadow">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <i class="fab fa-linkedin fa-3x text-primary mb-3"></i>
                                <h3 class="h4 text-gray-900">LinkedIn Automation Bot</h3>
                                <p class="text-muted">Sign in to your account</p>
                            </div>
                            
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <span class="spinner-border spinner-border-sm d-none me-2" id="loginSpinner"></span>
                                    Sign In
                                </button>
                            </form>
                            
                            <div class="text-center">
                                <a href="#" onclick="showRegisterForm()" class="text-decoration-none">
                                    Don't have an account? Register here
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add login form handler
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
}

/**
 * Show registration form
 */
function showRegisterForm() {
    document.body.innerHTML = `
        <div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
            <div class="row w-100">
                <div class="col-md-6 col-lg-4 mx-auto">
                    <div class="card shadow">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <i class="fab fa-linkedin fa-3x text-primary mb-3"></i>
                                <h3 class="h4 text-gray-900">Create Account</h3>
                                <p class="text-muted">Join LinkedIn Automation Bot</p>
                            </div>
                            
                            <form id="registerForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mb-3">
                                    <span class="spinner-border spinner-border-sm d-none me-2" id="registerSpinner"></span>
                                    Create Account
                                </button>
                            </form>
                            
                            <div class="text-center">
                                <a href="#" onclick="showLoginForm()" class="text-decoration-none">
                                    Already have an account? Sign in here
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add register form handler
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
}

/**
 * Handle login form submission
 */
async function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const spinner = document.getElementById('loginSpinner');
    
    try {
        spinner.classList.remove('d-none');
        
        const response = await apiCall('/auth/login', 'POST', {
            email: email,
            password: password
        });
        
        if (response && response.access_token) {
            authToken = response.access_token;
            localStorage.setItem('authToken', authToken);
            
            // Reload the page to initialize the dashboard
            window.location.reload();
        }
        
    } catch (error) {
        showNotification('Login failed: ' + error.message, 'error');
    } finally {
        spinner.classList.add('d-none');
    }
}

/**
 * Handle registration form submission
 */
async function handleRegister(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const spinner = document.getElementById('registerSpinner');
    
    try {
        spinner.classList.remove('d-none');
        
        const response = await apiCall('/auth/register', 'POST', {
            username: username,
            email: email,
            password: password
        });
        
        if (response) {
            showNotification('Account created successfully! Please sign in.', 'success');
            setTimeout(() => showLoginForm(), 2000);
        }
        
    } catch (error) {
        showNotification('Registration failed: ' + error.message, 'error');
    } finally {
        spinner.classList.add('d-none');
    }
}

/**
 * Logout user
 */
function logout() {
    localStorage.removeItem('authToken');
    authToken = null;
    currentUser = null;
    showLoginForm();
}

/**
 * Show notification
 */
function showNotification(message, type = 'info', duration = 5000) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show notification`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Find or create notifications container
    let container = document.getElementById('notifications-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notifications-container';
        container.style.position = 'fixed';
        container.style.top = '70px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        container.style.maxWidth = '400px';
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
}

/**
 * Navigation functions
 */
function showDashboard() {
    switchView('dashboard');
    loadDashboardData();
}

function showJobs() {
    console.log('showJobs called');
    switchView('jobs');
    console.log('Switched to jobs view, now loading jobs data...');
    loadJobsData();
}

function showApplications() {
    switchView('applications');
    loadApplicationsData();
}

function showResumes() {
    console.log('showResumes called');
    switchView('resumes');
    console.log('Switched to resumes view, now loading resumes data...');
    loadResumesData();
}

function showJobSearch() {
    switchView('job-search');
    loadJobSearchForm();
}

function showSettings() {
    switchView('settings');
    loadSettingsForm();
}

/**
 * Switch between views
 */
function switchView(viewName) {
    // Hide all views
    document.querySelectorAll('.content-view').forEach(view => {
        view.style.display = 'none';
    });
    
    // Show selected view
    const targetView = document.getElementById(viewName + '-view');
    if (targetView) {
        targetView.style.display = 'block';
    }
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Find and activate the corresponding nav link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        if (link.textContent.toLowerCase().includes(viewName.replace('-', ' '))) {
            link.classList.add('active');
        }
    });
    
    currentView = viewName;
}

/**
 * Start periodic updates
 */
function startPeriodicUpdates() {
    // Update notifications every 30 seconds
    setInterval(async () => {
        try {
            const notifications = await apiCall('/dashboard/notifications');
            updateNotifications(notifications);
        } catch (error) {
            console.error('Failed to update notifications:', error);
        }
    }, 30000);
    
    // Update dashboard data every 2 minutes if on dashboard
    setInterval(async () => {
        if (currentView === 'dashboard') {
            try {
                await loadDashboardData();
            } catch (error) {
                console.error('Failed to update dashboard:', error);
            }
        }
    }, 120000);
}

/**
 * Update notifications
 */
function updateNotifications(notificationsData) {
    if (!notificationsData || !notificationsData.notifications) {
        return;
    }
    
    notificationsData.notifications.forEach(notification => {
        showNotification(
            `<strong>${notification.title}</strong><br>${notification.message}`,
            notification.type,
            0 // Don't auto-dismiss
        );
    });
}

/**
 * Utility functions
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

function formatRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays < 7) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
}

function truncateText(text, maxLength = 100) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

/**
 * Loading state management
 */
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;
    }
}

function hideLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        const loading = container.querySelector('.loading');
        if (loading) {
            loading.remove();
        }
    }
}
