#!/usr/bin/env python3
"""
Simple LinkedIn test to diagnose issues
"""
import sys
sys.path.append('.')

def test_database_connection():
    """Test database connection and data"""
    try:
        print("💾 Testing Database Connection...")
        
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.job import Job
        
        db = SessionLocal()
        
        # Check users
        total_users = db.query(User).count()
        users_with_creds = db.query(User).filter(User.linkedin_email.isnot(None)).count()
        
        print(f"   👥 Total users: {total_users}")
        print(f"   🔑 Users with LinkedIn credentials: {users_with_creds}")
        
        # Check jobs
        total_jobs = db.query(Job).count()
        print(f"   💼 Total jobs: {total_jobs}")
        
        if total_jobs > 0:
            latest_job = db.query(Job).order_by(Job.date_posted.desc()).first()
            print(f"   📋 Latest job: {latest_job.title} at {latest_job.company}")
            print(f"   📅 Posted: {latest_job.date_posted}")
        
        # Test LinkedIn credentials
        if users_with_creds > 0:
            user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
            print(f"   📧 LinkedIn user: {user.email}")
            
            # Test credential decryption
            try:
                from app.core.security import decrypt_credentials
                creds = decrypt_credentials(user.linkedin_email, user.linkedin_password)
                print(f"   ✅ Credentials decryption: SUCCESS")
                print(f"   📧 LinkedIn email: {creds['email']}")
            except Exception as e:
                print(f"   ❌ Credentials decryption failed: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        return False

def test_webdriver_basic():
    """Test basic WebDriver functionality"""
    try:
        print("\n🔧 Testing WebDriver Basic Functionality...")
        
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        import os
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        # Try to find Chrome executable
        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe")
        ]
        
        chrome_path = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_path = path
                break
        
        if chrome_path:
            chrome_options.binary_location = chrome_path
            print(f"   ✅ Chrome found: {chrome_path}")
        else:
            print("   ⚠️  Chrome path not found, using system default")
        
        # Test WebDriver creation
        try:
            driver = webdriver.Chrome(options=chrome_options)
            print("   ✅ WebDriver created successfully")
            
            # Test basic navigation
            driver.get("https://www.google.com")
            title = driver.title
            print(f"   ✅ Navigation test: {title}")
            
            driver.quit()
            print("   ✅ WebDriver closed successfully")
            return True
            
        except Exception as e:
            print(f"   ❌ WebDriver test failed: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ WebDriver setup failed: {e}")
        return False

def test_linkedin_credentials():
    """Test LinkedIn credentials specifically"""
    try:
        print("\n🔐 Testing LinkedIn Credentials...")
        
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.core.security import decrypt_credentials
        
        db = SessionLocal()
        
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials")
            return False
        
        print(f"   👤 User: {user.email}")
        
        # Test decryption
        try:
            creds = decrypt_credentials(user.linkedin_email, user.linkedin_password)
            print(f"   ✅ LinkedIn email: {creds['email']}")
            print(f"   ✅ Password length: {len(creds['password'])} characters")
            
            # Validate email format
            if "@" in creds['email'] and "." in creds['email']:
                print("   ✅ Email format valid")
            else:
                print("   ⚠️  Email format questionable")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Credential decryption failed: {e}")
            return False
        
        finally:
            db.close()
        
    except Exception as e:
        print(f"   ❌ Credential test failed: {e}")
        return False

def test_server_endpoints():
    """Test server endpoints"""
    try:
        print("\n🌐 Testing Server Endpoints...")
        
        import requests
        
        endpoints = [
            ("/", "Main page"),
            ("/docs", "API documentation"),
            ("/api/jobs", "Jobs API")
        ]
        
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
                print(f"   ✅ {description}: {response.status_code}")
            except Exception as e:
                print(f"   ❌ {description}: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Server endpoint test failed: {e}")
        return False

def main():
    """Run simple diagnostic tests"""
    print("🧪 Simple LinkedIn Automation Diagnostic")
    print("=" * 50)
    
    # Test database
    db_ok = test_database_connection()
    
    # Test credentials
    creds_ok = test_linkedin_credentials()
    
    # Test WebDriver
    webdriver_ok = test_webdriver_basic()
    
    # Test server
    server_ok = test_server_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 Diagnostic Results:")
    print(f"   Database: {'✅' if db_ok else '❌'}")
    print(f"   LinkedIn Credentials: {'✅' if creds_ok else '❌'}")
    print(f"   WebDriver: {'✅' if webdriver_ok else '❌'}")
    print(f"   Server: {'✅' if server_ok else '❌'}")
    
    if all([db_ok, creds_ok, webdriver_ok, server_ok]):
        print("\n🎉 All components working! LinkedIn automation should be functional.")
        print("\n💡 If jobs aren't showing in web interface:")
        print("   1. LinkedIn may have changed their page structure")
        print("   2. 2FA might be required during job search")
        print("   3. Rate limiting or IP blocking by LinkedIn")
    else:
        print("\n⚠️  Some components need attention:")
        if not db_ok:
            print("   🔧 Fix database connection")
        if not creds_ok:
            print("   🔧 Re-enter LinkedIn credentials in web interface")
        if not webdriver_ok:
            print("   🔧 Install/update Chrome browser")
        if not server_ok:
            print("   🔧 Check server configuration")

if __name__ == "__main__":
    main()
