"""
Jobs API routes for job search and management
"""
import logging
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from pydantic import BaseModel

from app.core.database import get_db
from app.models.user import User
from app.models.job import Job
from app.api.auth import get_current_user
from app.services.job_service import JobService
from app.services.linkedin_bot import LinkedInBotService

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models
class JobSearchRequest(BaseModel):
    keywords: str
    location: Optional[str] = None
    date_posted: Optional[str] = "past_week"
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None


class JobResponse(BaseModel):
    id: int
    linkedin_job_id: str
    title: str
    company: str
    location: Optional[str]
    job_type: Optional[str]
    experience_level: Optional[str]
    description: Optional[str]
    salary_min: Optional[float]
    salary_max: Optional[float]
    salary_currency: Optional[str]
    date_posted: Optional[datetime]
    easy_apply: bool
    is_applied: bool
    is_bookmarked: bool
    match_score: Optional[float]
    url: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class JobListResponse(BaseModel):
    jobs: List[JobResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class JobSearchStatus(BaseModel):
    status: str
    message: str
    jobs_found: int
    search_id: Optional[str] = None


@router.post("/search", response_model=JobSearchStatus)
async def search_jobs(
    search_request: JobSearchRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a LinkedIn job search process"""
    try:
        # Check if user has LinkedIn credentials for real search
        if current_user.linkedin_email and current_user.linkedin_password:
            logger.info("Starting real LinkedIn job search for user %s", current_user.email)
        else:
            logger.info("No LinkedIn credentials found, will use mock data for user %s", current_user.email)
        
        # Start background job search
        search_id = f"search_{current_user.id}_{int(datetime.utcnow().timestamp())}"
        
        background_tasks.add_task(
            perform_job_search,
            search_id,
            search_request,
            current_user.id,
            db
        )
        
        logger.info("Job search started for user %s with keywords: %s", 
                   current_user.email, search_request.keywords)
        
        return JobSearchStatus(
            status="started",
            message="Job search started. Results will be available shortly.",
            jobs_found=0,
            search_id=search_id
        )
        
    except Exception as e:
        logger.error("Failed to start job search: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start job search"
        )


@router.get("/", response_model=JobListResponse)
async def get_jobs(
    page: int = 1,
    per_page: int = 20,
    keywords: Optional[str] = None,
    company: Optional[str] = None,
    location: Optional[str] = None,
    is_applied: Optional[bool] = None,
    is_bookmarked: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get paginated list of jobs for current user"""
    try:
        # Build query
        query = db.query(Job).filter(Job.user_id == current_user.id)
        
        # Apply filters
        if keywords:
            query = query.filter(Job.title.contains(keywords))
        if company:
            query = query.filter(Job.company.contains(company))
        if location:
            query = query.filter(Job.location.contains(location))
        if is_applied is not None:
            query = query.filter(Job.is_applied == is_applied)
        if is_bookmarked is not None:
            query = query.filter(Job.is_bookmarked == is_bookmarked)
        
        # Get total count
        total = query.count()
        
        # Apply pagination and ordering
        jobs = query.order_by(desc(Job.created_at)).offset((page - 1) * per_page).limit(per_page).all()
        
        # Calculate pagination info
        has_next = (page * per_page) < total
        has_prev = page > 1
        
        return JobListResponse(
            jobs=[JobResponse.from_orm(job) for job in jobs],
            total=total,
            page=page,
            per_page=per_page,
            has_next=has_next,
            has_prev=has_prev
        )
        
    except Exception as e:
        logger.error("Failed to get jobs: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve jobs"
        )


@router.get("/{job_id}", response_model=JobResponse)
async def get_job(
    job_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get specific job details"""
    job = db.query(Job).filter(
        and_(Job.id == job_id, Job.user_id == current_user.id)
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    return JobResponse.from_orm(job)


@router.post("/{job_id}/bookmark")
async def bookmark_job(
    job_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Bookmark or unbookmark a job"""
    job = db.query(Job).filter(
        and_(Job.id == job_id, Job.user_id == current_user.id)
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    job.is_bookmarked = not job.is_bookmarked
    job.updated_at = datetime.utcnow()
    db.commit()
    
    action = "bookmarked" if job.is_bookmarked else "unbookmarked"
    logger.info("Job %s %s by user %s", job_id, action, current_user.email)
    
    return {"message": f"Job {action} successfully", "is_bookmarked": job.is_bookmarked}


@router.delete("/{job_id}")
async def delete_job(
    job_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a job from the database"""
    job = db.query(Job).filter(
        and_(Job.id == job_id, Job.user_id == current_user.id)
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    db.delete(job)
    db.commit()
    
    logger.info("Job %s deleted by user %s", job_id, current_user.email)
    
    return {"message": "Job deleted successfully"}


async def perform_job_search(
    search_id: str,
    search_request: JobSearchRequest,
    user_id: int,
    db: Session
):
    """Background task to perform real LinkedIn job search"""
    try:
        logger.info("Starting LinkedIn job search: %s", search_id)

        # Get user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            logger.error("User not found for job search: %s", user_id)
            return

        # Check if user has LinkedIn credentials
        if not user.linkedin_email or not user.linkedin_password:
            logger.error("LinkedIn credentials not found for user %s", user.email)
            return

        # Initialize LinkedIn bot
        from app.services.linkedin_bot import LinkedInBotService
        linkedin_bot = LinkedInBotService(user, db)

        try:
            # Perform real LinkedIn job search
            jobs_found = await linkedin_bot.search_jobs(search_request)

            logger.info("LinkedIn job search completed: %d jobs found for search %s",
                       jobs_found, search_id)

        except Exception as e:
            logger.error("LinkedIn job search failed: %s", str(e))

            # Fallback to mock data if real search fails
            logger.info("Falling back to mock data for demonstration")
            jobs_found = await _create_mock_jobs(search_request, user_id, db)

        finally:
            # Always close the browser
            linkedin_bot.close()

    except Exception as e:
        logger.error("Background job search failed: %s", str(e))
        db.rollback()


async def _create_mock_jobs(search_request: JobSearchRequest, user_id: int, db: Session) -> int:
    """Create mock jobs as fallback"""
    import random
    from datetime import datetime, timedelta

    # Sample companies and job titles
    companies = ["Google", "Microsoft", "Amazon", "Apple", "Meta", "Netflix", "Tesla", "Spotify", "Uber", "Airbnb"]
    job_titles = [
        f"{search_request.keywords} Engineer",
        f"Senior {search_request.keywords}",
        f"{search_request.keywords} Developer",
        f"Lead {search_request.keywords}",
        f"{search_request.keywords} Specialist"
    ]

    jobs_created = 0

    # Create 3-8 mock jobs
    for i in range(random.randint(3, 8)):
        try:
            # Generate mock job data
            company = random.choice(companies)
            title = random.choice(job_titles)
            location = search_request.location or "Remote"

            # Create unique LinkedIn job ID
            linkedin_job_id = f"mock_{user_id}_{int(datetime.utcnow().timestamp())}_{i}"

            # Check if job already exists
            existing_job = db.query(Job).filter(
                Job.linkedin_job_id == linkedin_job_id,
                Job.user_id == user_id
            ).first()

            if existing_job:
                continue

            # Create job
            job = Job(
                user_id=user_id,
                linkedin_job_id=linkedin_job_id,
                title=title,
                company=company,
                location=location,
                url=f"https://linkedin.com/jobs/view/{linkedin_job_id}",
                easy_apply=random.choice([True, False]),
                date_posted=datetime.utcnow() - timedelta(days=random.randint(0, 7)),
                job_type=search_request.job_type or "Full-time",
                experience_level=search_request.experience_level or "Mid-level",
                salary_min=search_request.salary_min,
                salary_max=search_request.salary_max,
                description=f"Exciting opportunity for a {title} at {company}. We are looking for someone with experience in {search_request.keywords}."
            )

            db.add(job)
            jobs_created += 1

        except Exception as e:
            logger.warning("Failed to create mock job %d: %s", i, str(e))
            continue

    # Commit all jobs
    db.commit()
    return jobs_created
