#!/usr/bin/env python3
"""
Clear fake/mock jobs from database to see real LinkedIn data
"""
import sys
sys.path.append('.')

def clear_fake_jobs():
    """Remove all fake/mock jobs from database"""
    try:
        print("🧹 Clearing Fake Jobs from Database...")
        
        from app.core.database import SessionLocal
        from app.models.job import Job
        from app.models.user import User
        
        db = SessionLocal()
        
        # Get user
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
        
        print(f"   👤 User: {user.email}")
        
        # Count total jobs before
        total_jobs_before = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Total jobs before cleanup: {total_jobs_before}")
        
        # Find fake jobs (those with mock IDs or suspicious patterns)
        fake_jobs = db.query(Job).filter(
            Job.user_id == user.id
        ).filter(
            # Jobs with mock IDs
            Job.linkedin_job_id.like('mock_%')
        ).all()
        
        print(f"   🎭 Found {len(fake_jobs)} jobs with mock IDs")
        
        # Also find jobs with suspicious patterns
        suspicious_jobs = db.query(Job).filter(
            Job.user_id == user.id
        ).filter(
            # Jobs with repeated keywords in title (like "Devops Engineer Engineer")
            Job.title.like('%Engineer Engineer%') |
            Job.title.like('%Developer Developer%') |
            Job.title.like('%Specialist Specialist%')
        ).all()
        
        print(f"   🤔 Found {len(suspicious_jobs)} jobs with suspicious title patterns")
        
        # Combine and deduplicate
        all_fake_jobs = list(set(fake_jobs + suspicious_jobs))
        print(f"   🗑️  Total fake jobs to remove: {len(all_fake_jobs)}")
        
        if all_fake_jobs:
            print("   📋 Fake jobs being removed:")
            for i, job in enumerate(all_fake_jobs[:10], 1):  # Show first 10
                print(f"      {i}. {job.title} at {job.company}")
                print(f"         🆔 ID: {job.linkedin_job_id}")
                print(f"         📅 Posted: {job.date_posted}")
            
            if len(all_fake_jobs) > 10:
                print(f"      ... and {len(all_fake_jobs) - 10} more")
            
            # Delete fake jobs
            for job in all_fake_jobs:
                db.delete(job)
            
            db.commit()
            print(f"   ✅ Removed {len(all_fake_jobs)} fake jobs")
        else:
            print("   ℹ️  No fake jobs found to remove")
        
        # Count jobs after
        total_jobs_after = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Total jobs after cleanup: {total_jobs_after}")
        
        # Show remaining jobs
        if total_jobs_after > 0:
            remaining_jobs = db.query(Job).filter(Job.user_id == user.id).order_by(Job.date_posted.desc()).limit(5).all()
            print("   📋 Remaining jobs (if any):")
            for i, job in enumerate(remaining_jobs, 1):
                print(f"      {i}. {job.title} at {job.company}")
                print(f"         🆔 ID: {job.linkedin_job_id}")
                print(f"         📅 Posted: {job.date_posted}")
                print(f"         🔗 URL: {job.url}")
                print()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to clear fake jobs: {e}")
        return False

def main():
    """Clear fake jobs and prepare for real LinkedIn testing"""
    print("🧹 Fake Job Cleanup Utility")
    print("=" * 50)
    
    success = clear_fake_jobs()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Fake job cleanup completed!")
        print("\n🎯 Next steps:")
        print("   1. The database is now clean of fake jobs")
        print("   2. Try a new LinkedIn job search via web interface")
        print("   3. You should now see real LinkedIn jobs or clear error messages")
        print("   4. No more fake 'Devops Engineer Engineer' jobs!")
        print("\n💡 If you still see 0 jobs found, it means:")
        print("   - LinkedIn automation is working but WebDriver connection fails")
        print("   - We need to fix the WebDriver stability issue")
        print("   - But at least you'll see the real problem, not fake data")
    else:
        print("❌ Fake job cleanup failed")
        print("   🔧 Check database connection")
        print("   🔧 Verify user credentials")

if __name__ == "__main__":
    main()
