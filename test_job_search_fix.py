#!/usr/bin/env python3
"""
Test the job search fix
"""
import sys
import asyncio
sys.path.append('.')

async def test_job_search_function():
    """Test the job search function with the new error handling"""
    try:
        print("🔍 Testing Job Search Function with <PERSON>rro<PERSON> Handling...")
        
        # Import required modules
        from app.api.jobs import perform_job_search, _create_mock_jobs, JobSearchRequest
        from app.core.database import SessionLocal
        from app.models.user import User
        
        print("   ✅ Imports successful")
        
        # Get database session
        db = SessionLocal()
        
        # Get a user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ⚠️  No users with LinkedIn credentials found")
            # Get any user
            user = db.query(User).first()
            
        if not user:
            print("   ❌ No users found in database")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create a test search request
        search_request = JobSearchRequest(
            keywords="Python Developer",
            location="Remote",
            job_type="Full-time"
        )
        
        print("   ✅ Created search request")
        
        # Test the mock jobs function first
        print("   🧪 Testing mock jobs creation...")
        mock_jobs = await _create_mock_jobs(search_request, user.id, db)
        print(f"   ✅ Mock jobs created: {mock_jobs}")
        
        # Test the main job search function
        print("   🧪 Testing main job search function...")
        
        # This should now handle errors gracefully
        await perform_job_search(
            "test_search_123",
            search_request,
            user.id,
            db
        )
        
        print("   ✅ Job search function completed without crashing")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test"""
    print("🧪 Job Search Fix Test")
    print("=" * 40)
    
    success = await test_job_search_function()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Job search fix test passed!")
        print("\n🎯 The system should now work with fallback to mock data")
        print("   - If LinkedIn bot fails, it will use mock data")
        print("   - If AI service fails, it will continue without AI")
        print("   - The web interface should work properly")
    else:
        print("❌ Job search fix test failed")

if __name__ == "__main__":
    asyncio.run(main())
