#!/usr/bin/env python3
"""
Test improved LinkedIn job search with better error handling
"""
import sys
import asyncio
import requests
import json
sys.path.append('.')

async def test_improved_linkedin_job_search():
    """Test the improved LinkedIn job search"""
    try:
        print("🔍 Testing Improved LinkedIn Job Search...")
        
        # Test via API
        search_data = {
            "keywords": "Python Developer",
            "location": "Remote",
            "job_type": "Full-time"
        }
        
        print(f"   🔍 Keywords: {search_data['keywords']}")
        print(f"   📍 Location: {search_data['location']}")
        print("   🚀 Starting job search...")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/jobs/search",
                json=search_data,
                headers={"Content-Type": "application/json"},
                timeout=180  # 3 minutes timeout
            )
            
            print(f"   📡 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Search initiated: {result}")
                return True
            else:
                print(f"   ❌ API Error: {response.status_code}")
                print(f"   📄 Response: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("   ⏰ Request timed out - search may still be running")
            return True  # Consider timeout as partial success
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

def check_jobs_in_database():
    """Check if any jobs were added to database"""
    try:
        print("\n💾 Checking Database for New Jobs...")
        
        from app.core.database import SessionLocal
        from app.models.job import Job
        from app.models.user import User
        
        db = SessionLocal()
        
        # Get user
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        if not user:
            print("   ❌ No users with LinkedIn credentials")
            return False
        
        # Count total jobs
        total_jobs = db.query(Job).filter(Job.user_id == user.id).count()
        print(f"   📊 Total jobs for {user.email}: {total_jobs}")
        
        # Show recent jobs
        recent_jobs = db.query(Job).filter(Job.user_id == user.id).order_by(Job.date_posted.desc()).limit(5).all()
        
        if recent_jobs:
            print("   📋 Recent jobs:")
            for i, job in enumerate(recent_jobs, 1):
                print(f"      {i}. {job.title}")
                print(f"         🏢 {job.company}")
                print(f"         📍 {job.location or 'Location not specified'}")
                print(f"         📅 {job.date_posted.strftime('%Y-%m-%d %H:%M')}")
                print()
        
        db.close()
        return total_jobs > 0
        
    except Exception as e:
        print(f"   ❌ Database check failed: {e}")
        return False

def monitor_server_logs():
    """Monitor server logs for job search activity"""
    try:
        print("\n📋 Monitoring Recent Server Activity...")
        
        import os
        import time
        
        log_file = "logs/linkedin_bot_20250531.log"
        
        if not os.path.exists(log_file):
            print("   ⚠️  Log file not found")
            return
        
        # Read recent lines
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            recent_lines = lines[-20:] if len(lines) > 20 else lines
        
        print("   📄 Recent activity:")
        
        for line in recent_lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in [
                'job search', 'linkedin', 'webdriver', 'found', 'saved', 'error', 'failed'
            ]):
                if "ERROR" in line:
                    print(f"   ❌ {line}")
                elif "WARNING" in line:
                    print(f"   ⚠️  {line}")
                elif any(word in line.lower() for word in ['found', 'saved', 'successful']):
                    print(f"   ✅ {line}")
                else:
                    print(f"   ℹ️  {line}")
        
    except Exception as e:
        print(f"   ❌ Log monitoring failed: {e}")

async def wait_and_check_results():
    """Wait for job search to complete and check results"""
    try:
        print("\n⏳ Waiting for Job Search to Complete...")
        
        # Wait for search to process
        for i in range(12):  # Wait up to 2 minutes
            print(f"   ⏳ Waiting... {i*10}s")
            await asyncio.sleep(10)
            
            # Check if jobs were added
            from app.core.database import SessionLocal
            from app.models.job import Job
            from app.models.user import User
            
            db = SessionLocal()
            user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
            
            if user:
                jobs_count = db.query(Job).filter(Job.user_id == user.id).count()
                print(f"   📊 Current job count: {jobs_count}")
                
                # Check for very recent jobs (last 5 minutes)
                from datetime import datetime, timedelta
                recent_time = datetime.utcnow() - timedelta(minutes=5)
                recent_jobs = db.query(Job).filter(
                    Job.user_id == user.id,
                    Job.date_posted >= recent_time
                ).count()
                
                if recent_jobs > 0:
                    print(f"   🎉 Found {recent_jobs} new jobs in last 5 minutes!")
                    db.close()
                    return True
            
            db.close()
        
        print("   ⏰ Timeout reached")
        return False
        
    except Exception as e:
        print(f"   ❌ Wait and check failed: {e}")
        return False

async def main():
    """Run comprehensive job search test"""
    print("🧪 Improved LinkedIn Job Search Test")
    print("=" * 60)
    
    # Check initial state
    initial_jobs = check_jobs_in_database()
    
    # Monitor logs before test
    monitor_server_logs()
    
    # Start job search
    print("\n" + "=" * 60)
    search_started = await test_improved_linkedin_job_search()
    
    if search_started:
        # Wait and monitor results
        print("\n" + "=" * 60)
        jobs_found = await wait_and_check_results()
        
        # Final check
        print("\n" + "=" * 60)
        final_jobs = check_jobs_in_database()
        
        # Monitor logs after test
        monitor_server_logs()
        
        print("\n" + "=" * 60)
        print("📊 Final Results:")
        print(f"   Search Started: {'✅' if search_started else '❌'}")
        print(f"   Jobs Found: {'✅' if jobs_found else '❌'}")
        print(f"   Database Updated: {'✅' if final_jobs else '❌'}")
        
        if search_started and (jobs_found or final_jobs):
            print("\n🎉 SUCCESS! LinkedIn job search is working!")
            print("\n🎯 Improvements made:")
            print("   ✅ Better WebDriver connection handling")
            print("   ✅ Enhanced error recovery")
            print("   ✅ Fallback data extraction")
            print("   ✅ Improved logging and monitoring")
            print("\n💡 The LinkedIn automation is now more stable and reliable!")
        else:
            print("\n⚠️  Job search needs further attention:")
            print("   🔧 Check LinkedIn credentials")
            print("   🔧 Verify 2FA completion")
            print("   🔧 Check internet connection")
            print("   🔧 LinkedIn may have updated their page structure")
    else:
        print("\n❌ Could not start job search")
        print("   🔧 Check if server is running")
        print("   🔧 Verify API endpoints")

if __name__ == "__main__":
    asyncio.run(main())
