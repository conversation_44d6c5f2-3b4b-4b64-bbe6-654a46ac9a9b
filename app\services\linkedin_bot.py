"""
LinkedIn automation service using Selenium
"""
import logging
import time
import random
import asyncio
import os
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import decrypt_credentials
from app.models.user import User
from app.models.job import Job
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class LinkedInBotService:
    """LinkedIn automation service for job searching and application"""
    
    def __init__(self, user: User, db: Session):
        self.user = user
        self.db = db
        self.driver = None
        self.wait = None

        # Initialize AI service with error handling
        try:
            self.ai_service = AIService()
            logger.info("AI service initialized for LinkedIn bot")
        except Exception as e:
            logger.warning("Failed to initialize AI service: %s. Continuing without AI features.", str(e))
            self.ai_service = None
        
    async def initialize_driver(self):
        """Initialize Chrome WebDriver with comprehensive error handling"""
        try:
            logger.info("Initializing Chrome WebDriver...")

            # Check if Chrome is available
            chrome_options = Options()

            # Add comprehensive Chrome options for better compatibility
            if settings.HEADLESS_BROWSER:
                chrome_options.add_argument("--headless")
                logger.info("Running in headless mode")

            # Essential Chrome arguments for automation
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")
            chrome_options.add_argument("--disable-javascript")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Try multiple approaches to initialize WebDriver
            driver_initialized = False

            # Approach 1: Use ChromeDriverManager with cache clearing
            try:
                logger.info("Attempting ChromeDriverManager approach...")

                # Clear cache and force fresh download
                from webdriver_manager.chrome import ChromeDriverManager
                from webdriver_manager.core.utils import ChromeType

                driver_path = ChromeDriverManager(
                    cache_valid_range=0  # Force fresh download
                ).install()

                logger.info(f"ChromeDriver path: {driver_path}")

                # Verify the downloaded file is valid
                if os.path.exists(driver_path) and os.path.getsize(driver_path) > 1000:
                    service = Service(driver_path)
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    driver_initialized = True
                    logger.info("ChromeDriverManager approach successful")
                else:
                    raise Exception(f"Downloaded ChromeDriver appears invalid: {driver_path}")

            except Exception as e:
                logger.warning(f"ChromeDriverManager approach failed: {e}")

            # Approach 2: Try system Chrome installation
            if not driver_initialized:
                try:
                    logger.info("Attempting system Chrome approach...")
                    self.driver = webdriver.Chrome(options=chrome_options)
                    driver_initialized = True
                    logger.info("System Chrome approach successful")

                except Exception as e:
                    logger.warning(f"System Chrome approach failed: {e}")

            # Approach 3: Try with explicit Chrome binary path
            if not driver_initialized:
                try:
                    logger.info("Attempting explicit Chrome binary approach...")

                    # Common Chrome installation paths on Windows
                    chrome_paths = [
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.environ.get('USERNAME', '')),
                    ]

                    for chrome_path in chrome_paths:
                        if os.path.exists(chrome_path):
                            chrome_options.binary_location = chrome_path
                            logger.info(f"Found Chrome at: {chrome_path}")
                            break

                    self.driver = webdriver.Chrome(options=chrome_options)
                    driver_initialized = True
                    logger.info("Explicit Chrome binary approach successful")

                except Exception as e:
                    logger.warning(f"Explicit Chrome binary approach failed: {e}")

            if not driver_initialized:
                raise Exception("All WebDriver initialization approaches failed")

            # Set up WebDriverWait
            self.wait = WebDriverWait(self.driver, settings.BROWSER_TIMEOUT)

            # Test the driver with a simple navigation (optional)
            try:
                logger.info("Testing WebDriver with basic navigation...")
                self.driver.get("data:text/html,<html><head><title>Test</title></head><body>WebDriver Test</body></html>")

                if "Test" in self.driver.title:
                    logger.info("Chrome WebDriver initialized and tested successfully")
                else:
                    logger.warning("WebDriver test navigation had unexpected result, but driver is functional")

            except Exception as e:
                logger.warning(f"WebDriver test navigation failed: {e}, but driver appears functional")
                # Don't fail initialization just because of navigation test

        except Exception as e:
            logger.error("Failed to initialize WebDriver: %s", str(e))
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
            raise
    
    async def login(self) -> bool:
        """Login to LinkedIn"""
        try:
            if not self.user.linkedin_email or not self.user.linkedin_password:
                logger.error("LinkedIn credentials not found for user %s", self.user.email)
                return False
            
            # Decrypt credentials
            credentials = decrypt_credentials(
                self.user.linkedin_email,
                self.user.linkedin_password
            )
            
            logger.info("Attempting LinkedIn login for user %s", self.user.email)
            
            # Navigate to LinkedIn login page
            self.driver.get("https://www.linkedin.com/login")
            await asyncio.sleep(random.uniform(2, 4))
            
            # Enter email
            email_field = self.wait.until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            email_field.clear()
            email_field.send_keys(credentials["email"])
            await asyncio.sleep(random.uniform(1, 2))
            
            # Enter password
            password_field = self.driver.find_element(By.ID, "password")
            password_field.clear()
            password_field.send_keys(credentials["password"])
            await asyncio.sleep(random.uniform(1, 2))
            
            # Click login button
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # Wait for login to complete
            await asyncio.sleep(random.uniform(3, 5))
            
            # Check if login was successful
            if "feed" in self.driver.current_url or "mynetwork" in self.driver.current_url:
                logger.info("LinkedIn login successful")
                return True
            else:
                # Check for 2FA or other challenges
                if "challenge" in self.driver.current_url:
                    logger.warning("LinkedIn 2FA challenge detected. Manual intervention required.")
                    # Wait for manual intervention
                    await asyncio.sleep(30)
                    
                    # Check again
                    if "feed" in self.driver.current_url:
                        logger.info("LinkedIn login successful after manual intervention")
                        return True
                
                logger.error("LinkedIn login failed")
                return False
                
        except Exception as e:
            logger.error("LinkedIn login error: %s", str(e))
            return False
    
    async def search_jobs(self, search_request) -> int:
        """Search for jobs on LinkedIn"""
        try:
            if not self.driver:
                await self.initialize_driver()
            
            if not await self.login():
                return 0
            
            logger.info("Starting job search: %s", search_request.keywords)
            
            # Navigate to jobs page
            self.driver.get("https://www.linkedin.com/jobs/")
            await asyncio.sleep(random.uniform(2, 4))
            
            # Enter search keywords
            search_box = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[aria-label*='Search jobs']"))
            )
            search_box.clear()
            search_box.send_keys(search_request.keywords)
            await asyncio.sleep(random.uniform(1, 2))
            
            # Enter location if provided
            if search_request.location:
                location_box = self.driver.find_element(By.CSS_SELECTOR, "input[aria-label*='City']")
                location_box.clear()
                location_box.send_keys(search_request.location)
                await asyncio.sleep(random.uniform(1, 2))
            
            # Click search button
            search_button = self.driver.find_element(By.CSS_SELECTOR, "button[aria-label*='Search']")
            search_button.click()
            await asyncio.sleep(random.uniform(3, 5))
            
            # Apply filters
            await self._apply_search_filters(search_request)
            
            # Extract job listings
            jobs_found = await self._extract_job_listings()
            
            logger.info("Job search completed. Found %d jobs", jobs_found)
            return jobs_found
            
        except Exception as e:
            logger.error("Job search failed: %s", str(e))
            return 0
        finally:
            if self.driver:
                self.driver.quit()
    
    async def _apply_search_filters(self, search_request):
        """Apply search filters"""
        try:
            # Date posted filter
            if search_request.date_posted:
                date_filter_map = {
                    "past_24_hours": "Past 24 hours",
                    "past_week": "Past week",
                    "past_month": "Past month"
                }
                
                if search_request.date_posted in date_filter_map:
                    # Click on date filter
                    try:
                        date_filter = self.driver.find_element(
                            By.XPATH, f"//button[contains(text(), 'Date posted')]"
                        )
                        date_filter.click()
                        await asyncio.sleep(random.uniform(1, 2))
                        
                        # Select specific date range
                        date_option = self.driver.find_element(
                            By.XPATH, f"//label[contains(text(), '{date_filter_map[search_request.date_posted]}')]"
                        )
                        date_option.click()
                        await asyncio.sleep(random.uniform(1, 2))
                        
                    except NoSuchElementException:
                        logger.warning("Date filter not found, continuing without it")
            
            # Easy Apply filter
            try:
                easy_apply_filter = self.driver.find_element(
                    By.XPATH, "//button[contains(text(), 'Easy Apply')]"
                )
                easy_apply_filter.click()
                await asyncio.sleep(random.uniform(1, 2))
                
            except NoSuchElementException:
                logger.warning("Easy Apply filter not found")
            
        except Exception as e:
            logger.warning("Failed to apply some filters: %s", str(e))
    
    async def _extract_job_listings(self) -> int:
        """Extract job listings from search results"""
        jobs_found = 0
        page = 1
        max_pages = 5  # Limit to prevent excessive scraping
        
        try:
            while page <= max_pages:
                logger.info("Extracting jobs from page %d", page)
                
                # Wait for job listings to load
                await asyncio.sleep(random.uniform(2, 4))
                
                # Find job cards
                job_cards = self.driver.find_elements(
                    By.CSS_SELECTOR, 
                    "div[data-job-id], .job-search-card, .jobs-search-results__list-item"
                )
                
                if not job_cards:
                    logger.warning("No job cards found on page %d", page)
                    break
                
                for card in job_cards:
                    try:
                        job_data = await self._extract_job_data(card)
                        if job_data:
                            await self._save_job(job_data)
                            jobs_found += 1
                            
                    except Exception as e:
                        logger.warning("Failed to extract job data: %s", str(e))
                        continue
                
                # Try to go to next page
                try:
                    next_button = self.driver.find_element(
                        By.CSS_SELECTOR, 
                        "button[aria-label*='Next'], .artdeco-pagination__button--next"
                    )
                    
                    if next_button.is_enabled():
                        next_button.click()
                        await asyncio.sleep(random.uniform(3, 5))
                        page += 1
                    else:
                        break
                        
                except NoSuchElementException:
                    logger.info("No more pages available")
                    break
            
        except Exception as e:
            logger.error("Failed to extract job listings: %s", str(e))
        
        return jobs_found
    
    async def _extract_job_data(self, job_card) -> Optional[Dict]:
        """Extract data from a single job card"""
        try:
            # Get job ID
            job_id = job_card.get_attribute("data-job-id")
            if not job_id:
                # Try alternative selectors
                job_link = job_card.find_element(By.CSS_SELECTOR, "a[href*='/jobs/view/']")
                job_url = job_link.get_attribute("href")
                job_id = job_url.split("/jobs/view/")[1].split("?")[0] if "/jobs/view/" in job_url else None
            
            if not job_id:
                return None
            
            # Check if job already exists
            existing_job = self.db.query(Job).filter(
                Job.linkedin_job_id == job_id,
                Job.user_id == self.user.id
            ).first()
            
            if existing_job:
                return None  # Skip duplicate
            
            # Extract job details
            title_element = job_card.find_element(By.CSS_SELECTOR, "h3, .job-search-card__title a")
            title = title_element.text.strip()
            
            company_element = job_card.find_element(By.CSS_SELECTOR, ".job-search-card__subtitle, h4")
            company = company_element.text.strip()
            
            # Location (optional)
            location = None
            try:
                location_element = job_card.find_element(By.CSS_SELECTOR, ".job-search-card__location")
                location = location_element.text.strip()
            except NoSuchElementException:
                pass
            
            # Job URL
            job_link = job_card.find_element(By.CSS_SELECTOR, "a[href*='/jobs/view/']")
            job_url = job_link.get_attribute("href")
            
            # Easy Apply check
            easy_apply = False
            try:
                easy_apply_element = job_card.find_element(By.CSS_SELECTOR, ".job-search-card__easy-apply")
                easy_apply = True
            except NoSuchElementException:
                pass
            
            return {
                "linkedin_job_id": job_id,
                "title": title,
                "company": company,
                "location": location,
                "url": job_url,
                "easy_apply": easy_apply,
                "date_posted": datetime.utcnow()
            }
            
        except Exception as e:
            logger.warning("Failed to extract job data from card: %s", str(e))
            return None
    
    async def _save_job(self, job_data: Dict):
        """Save job to database"""
        try:
            job = Job(
                user_id=self.user.id,
                linkedin_job_id=job_data["linkedin_job_id"],
                title=job_data["title"],
                company=job_data["company"],
                location=job_data.get("location"),
                url=job_data["url"],
                easy_apply=job_data["easy_apply"],
                date_posted=job_data["date_posted"]
            )
            
            self.db.add(job)
            self.db.commit()
            
            logger.debug("Saved job: %s at %s", job.title, job.company)
            
        except Exception as e:
            logger.error("Failed to save job: %s", str(e))
            self.db.rollback()
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            self.driver = None
