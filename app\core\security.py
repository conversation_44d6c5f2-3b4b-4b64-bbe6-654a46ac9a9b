"""
Security utilities for encryption, hashing, and authentication
"""
import os
import base64
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Op<PERSON>, Union
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from passlib.context import Crypt<PERSON>ontext
from jose import JWTError, jwt

from app.core.config import settings

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class EncryptionManager:
    """Handles AES encryption for sensitive data"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self) -> bytes:
        """Get existing encryption key or create a new one"""
        if settings.ENCRYPTION_KEY and settings.ENCRYPTION_KEY != "your-encryption-key-base64-encoded":
            try:
                # Use provided key
                return base64.urlsafe_b64decode(settings.ENCRYPTION_KEY.encode())
            except Exception as e:
                logger.error("Invalid encryption key provided: %s", str(e))
                # Fall through to generate new key

        # Generate new key
        key = Fernet.generate_key()
        logger.warning("Generated new encryption key. Store this securely: %s",
                      base64.urlsafe_b64encode(key).decode())
        return key
    
    def encrypt(self, data: str) -> str:
        """Encrypt string data"""
        if not data:
            return ""
        
        try:
            encrypted_data = self.cipher.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error("Encryption failed: %s", str(e))
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt string data"""
        if not encrypted_data:
            return ""
        
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error("Decryption failed: %s", str(e))
            raise


# Global encryption manager instance
encryption_manager = EncryptionManager()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token and return payload"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError as e:
        logger.error("Token verification failed: %s", str(e))
        return None


def encrypt_credentials(email: str, password: str) -> dict:
    """Encrypt LinkedIn credentials"""
    return {
        "email": encryption_manager.encrypt(email),
        "password": encryption_manager.encrypt(password)
    }


def decrypt_credentials(encrypted_email: str, encrypted_password: str) -> dict:
    """Decrypt LinkedIn credentials"""
    return {
        "email": encryption_manager.decrypt(encrypted_email),
        "password": encryption_manager.decrypt(encrypted_password)
    }
