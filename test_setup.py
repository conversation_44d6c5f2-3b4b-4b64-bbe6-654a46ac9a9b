#!/usr/bin/env python3
"""
Test script to verify LinkedIn Automation Bot POC setup
"""

import sys
import os
import asyncio
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"❌ SQLAlchemy import failed: {e}")
        return False
    
    try:
        import selenium
        print("✅ Selenium imported successfully")
    except ImportError as e:
        print(f"❌ Selenium import failed: {e}")
        return False
    
    try:
        import openai
        print("✅ OpenAI imported successfully")
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        return False
    
    try:
        import cryptography
        print("✅ Cryptography imported successfully")
    except ImportError as e:
        print(f"❌ Cryptography import failed: {e}")
        return False
    
    return True

def test_app_structure():
    """Test if application structure is correct"""
    print("\nTesting application structure...")
    
    required_dirs = [
        "app",
        "app/api",
        "app/core",
        "app/models",
        "app/services",
        "frontend",
        "frontend/templates",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js"
    ]
    
    required_files = [
        "app/main.py",
        "app/core/config.py",
        "app/core/database.py",
        "app/core/security.py",
        "app/api/auth.py",
        "app/api/jobs.py",
        "app/api/applications.py",
        "app/api/dashboard.py",
        "app/services/linkedin_bot.py",
        "app/services/ai_service.py",
        "frontend/templates/index.html",
        "requirements.txt",
        "docker-compose.yml",
        "Dockerfile"
    ]
    
    all_good = True
    
    for directory in required_dirs:
        if Path(directory).exists():
            print(f"✅ Directory exists: {directory}")
        else:
            print(f"❌ Directory missing: {directory}")
            all_good = False
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ File exists: {file_path}")
        else:
            print(f"❌ File missing: {file_path}")
            all_good = False
    
    return all_good

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from app.core.config import settings
        print(f"✅ Configuration loaded successfully")
        print(f"   - App name: {settings.APP_NAME}")
        print(f"   - Debug mode: {settings.DEBUG}")
        print(f"   - Database URL: {settings.DATABASE_URL}")
        return True
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_database():
    """Test database initialization"""
    print("\nTesting database...")
    
    try:
        from app.core.database import engine, SessionLocal
        print("✅ Database engine created successfully")
        
        # Test connection
        with SessionLocal() as session:
            session.execute("SELECT 1")
        print("✅ Database connection successful")
        
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

async def test_database_init():
    """Test database initialization"""
    print("\nTesting database initialization...")
    
    try:
        from app.core.database import init_db
        await init_db()
        print("✅ Database tables created successfully")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def test_security():
    """Test security functions"""
    print("\nTesting security...")
    
    try:
        from app.core.security import encryption_manager, get_password_hash, verify_password
        
        # Test password hashing
        password = "test_password"
        hashed = get_password_hash(password)
        if verify_password(password, hashed):
            print("✅ Password hashing works correctly")
        else:
            print("❌ Password hashing failed")
            return False
        
        # Test encryption
        test_data = "sensitive_data"
        encrypted = encryption_manager.encrypt(test_data)
        decrypted = encryption_manager.decrypt(encrypted)
        
        if decrypted == test_data:
            print("✅ Encryption/decryption works correctly")
        else:
            print("❌ Encryption/decryption failed")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Security test failed: {e}")
        return False

def test_api_imports():
    """Test API module imports"""
    print("\nTesting API imports...")
    
    try:
        from app.api import auth, jobs, applications, dashboard
        print("✅ API modules imported successfully")
        return True
    except Exception as e:
        print(f"❌ API import failed: {e}")
        return False

def test_services_imports():
    """Test services imports"""
    print("\nTesting services imports...")
    
    try:
        from app.services import ai_service, job_service, application_service, resume_service
        print("✅ Services imported successfully")
        return True
    except Exception as e:
        print(f"❌ Services import failed: {e}")
        return False

def test_models_imports():
    """Test models imports"""
    print("\nTesting models imports...")
    
    try:
        from app.models import user, job, application, resume
        print("✅ Models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Models import failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("=" * 60)
    print("LinkedIn Automation Bot POC - Setup Verification")
    print("=" * 60)
    
    tests = [
        ("Package Imports", test_imports),
        ("Application Structure", test_app_structure),
        ("Configuration", test_config),
        ("Database Connection", test_database),
        ("Security Functions", test_security),
        ("API Imports", test_api_imports),
        ("Services Imports", test_services_imports),
        ("Models Imports", test_models_imports),
    ]
    
    async_tests = [
        ("Database Initialization", test_database_init),
    ]
    
    results = []
    
    # Run synchronous tests
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Run asynchronous tests
    for test_name, test_func in async_tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your API keys")
        print("2. Run: python -m uvicorn app.main:app --reload")
        print("3. Open http://localhost:8000 in your browser")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("Run 'python setup.py' to fix common issues.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
