2025-06-01 20:35:49 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-06-01 20:35:49 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-06-01 20:35:49 - app.main - INFO - lifespan:29 - Database initialized
2025-06-01 20:57:57 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-01 20:57:58 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-06-01 20:58:22 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-06-01 20:58:22 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops Engineer
2025-06-01 20:58:22 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748833102
2025-06-01 20:58:22 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - __init__:42 - AI service initialized for LinkedIn bot
2025-06-01 20:58:22 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - initialize_driver:50 - Initializing Chrome WebDriver...
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - initialize_driver:58 - Running in headless mode
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - initialize_driver:93 - Attempting ChromeDriverManager approach...
2025-06-01 20:58:22 - WDM - INFO - log:11 - ====== WebDriver manager ======
2025-06-01 20:58:24 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-06-01 20:58:24 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-06-01 20:58:24 - WDM - INFO - log:11 - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-01 20:58:24 - app.services.linkedin_bot - INFO - initialize_driver:100 - ChromeDriver path: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-01 20:58:24 - app.services.linkedin_bot - WARNING - initialize_driver:112 - ChromeDriverManager approach failed: [WinError 193] %1 is not a valid Win32 application
2025-06-01 20:58:24 - app.services.linkedin_bot - INFO - initialize_driver:117 - Attempting system Chrome approach...
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - initialize_driver:120 - System Chrome approach successful
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - initialize_driver:158 - Testing WebDriver with basic navigation...
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - initialize_driver:162 - Chrome WebDriver initialized and tested successfully
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - login:193 - Attempting LinkedIn login <NAME_EMAIL>
2025-06-01 20:58:42 - app.services.linkedin_bot - INFO - login:222 - LinkedIn login successful
2025-06-01 20:58:42 - app.services.linkedin_bot - INFO - search_jobs:276 - Starting job search: Devops Engineer
2025-06-01 20:59:51 - app.services.linkedin_bot - INFO - search_jobs:295 - Found search box with selector: .jobs-search-box__text-input
2025-06-01 20:59:54 - app.services.linkedin_bot - INFO - search_jobs:312 - Entered search keywords: Devops Engineer
