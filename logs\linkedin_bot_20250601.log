2025-06-01 20:35:49 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-06-01 20:35:49 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-06-01 20:35:49 - app.main - INFO - lifespan:29 - Database initialized
2025-06-01 20:57:57 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-01 20:57:58 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-06-01 20:58:22 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-06-01 20:58:22 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops Engineer
2025-06-01 20:58:22 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748833102
2025-06-01 20:58:22 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - __init__:42 - AI service initialized for LinkedIn bot
2025-06-01 20:58:22 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - initialize_driver:50 - Initializing Chrome WebDriver...
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - initialize_driver:58 - Running in headless mode
2025-06-01 20:58:22 - app.services.linkedin_bot - INFO - initialize_driver:93 - Attempting ChromeDriverManager approach...
2025-06-01 20:58:22 - WDM - INFO - log:11 - ====== WebDriver manager ======
2025-06-01 20:58:24 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-06-01 20:58:24 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-06-01 20:58:24 - WDM - INFO - log:11 - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-01 20:58:24 - app.services.linkedin_bot - INFO - initialize_driver:100 - ChromeDriver path: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-01 20:58:24 - app.services.linkedin_bot - WARNING - initialize_driver:112 - ChromeDriverManager approach failed: [WinError 193] %1 is not a valid Win32 application
2025-06-01 20:58:24 - app.services.linkedin_bot - INFO - initialize_driver:117 - Attempting system Chrome approach...
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - initialize_driver:120 - System Chrome approach successful
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - initialize_driver:158 - Testing WebDriver with basic navigation...
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - initialize_driver:162 - Chrome WebDriver initialized and tested successfully
2025-06-01 20:58:26 - app.services.linkedin_bot - INFO - login:193 - Attempting LinkedIn login <NAME_EMAIL>
2025-06-01 20:58:42 - app.services.linkedin_bot - INFO - login:222 - LinkedIn login successful
2025-06-01 20:58:42 - app.services.linkedin_bot - INFO - search_jobs:276 - Starting job search: Devops Engineer
2025-06-01 20:59:51 - app.services.linkedin_bot - INFO - search_jobs:295 - Found search box with selector: .jobs-search-box__text-input
2025-06-01 20:59:54 - app.services.linkedin_bot - INFO - search_jobs:312 - Entered search keywords: Devops Engineer
2025-06-01 20:59:56 - app.services.linkedin_bot - INFO - search_jobs:335 - Entered location: Canada
2025-06-01 21:01:09 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350D30790>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element
2025-06-01 21:01:13 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350D30880>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element
2025-06-01 21:01:18 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA49D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element
2025-06-01 21:01:26 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA60E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element
2025-06-01 21:01:30 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA5780>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element
2025-06-01 21:01:34 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA5A50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element
2025-06-01 21:01:42 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA5FC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element/f.1E0181B47A2AEEBC9C1F00B79BAD0E2B.d.B995B17D14E8AFA36FADBD9F2E400148.e.126/value
2025-06-01 21:01:46 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA4DF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element/f.1E0181B47A2AEEBC9C1F00B79BAD0E2B.d.B995B17D14E8AFA36FADBD9F2E400148.e.126/value
2025-06-01 21:01:50 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350D30280>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2/element/f.1E0181B47A2AEEBC9C1F00B79BAD0E2B.d.B995B17D14E8AFA36FADBD9F2E400148.e.126/value
2025-06-01 21:01:54 - app.services.linkedin_bot - ERROR - search_jobs:383 - Job search failed: HTTPConnectionPool(host='localhost', port=56256): Max retries exceeded with url: /session/fa78c1991e29e7a498ab5ae02638acf2/element/f.1E0181B47A2AEEBC9C1F00B79BAD0E2B.d.B995B17D14E8AFA36FADBD9F2E400148.e.126/value (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350D300A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-01 21:01:54 - app.api.jobs - INFO - perform_job_search:278 - LinkedIn job search completed: 0 jobs found for search search_1_1748833102
2025-06-01 21:01:58 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA57E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2
2025-06-01 21:02:02 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA46D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2
2025-06-01 21:02:06 - urllib3.connectionpool - WARNING - urlopen:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000027350DA6170>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/fa78c1991e29e7a498ab5ae02638acf2
2025-06-01 21:02:20 - app.main - INFO - lifespan:32 - Shutting down LinkedIn Automation Bot POC...
2025-06-01 21:02:27 - app.main - INFO - lifespan:27 - Starting LinkedIn Automation Bot POC...
2025-06-01 21:02:27 - app.core.database - INFO - init_db:49 - Database tables created successfully
2025-06-01 21:02:27 - app.main - INFO - lifespan:29 - Database initialized
2025-06-01 21:13:55 - passlib.handlers.bcrypt - WARNING - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-01 21:13:55 - app.api.auth - INFO - login:159 - User logged in: <EMAIL>
2025-06-01 21:14:10 - app.api.jobs - INFO - search_jobs:85 - Starting real LinkedIn job search <NAME_EMAIL>
2025-06-01 21:14:10 - app.api.jobs - INFO - search_jobs:100 - Job search started <NAME_EMAIL> with keywords: Devops Engineer
2025-06-01 21:14:10 - app.api.jobs - INFO - perform_job_search:254 - Starting LinkedIn job search: search_1_1748834050
2025-06-01 21:14:10 - app.services.ai_service - WARNING - __init__:20 - OpenAI API key not configured. AI features will be disabled.
2025-06-01 21:14:10 - app.services.linkedin_bot - INFO - __init__:42 - AI service initialized for LinkedIn bot
2025-06-01 21:14:10 - app.api.jobs - INFO - perform_job_search:272 - LinkedIn bot initialized successfully
2025-06-01 21:14:10 - app.services.linkedin_bot - INFO - initialize_driver:50 - Initializing Chrome WebDriver...
2025-06-01 21:14:10 - app.services.linkedin_bot - INFO - initialize_driver:58 - Running in headless mode
2025-06-01 21:14:10 - app.services.linkedin_bot - INFO - initialize_driver:93 - Attempting ChromeDriverManager approach...
2025-06-01 21:14:10 - WDM - INFO - log:11 - ====== WebDriver manager ======
2025-06-01 21:14:12 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-06-01 21:14:12 - WDM - INFO - log:11 - Get LATEST chromedriver version for google-chrome
2025-06-01 21:14:12 - WDM - INFO - log:11 - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-01 21:14:12 - app.services.linkedin_bot - INFO - initialize_driver:100 - ChromeDriver path: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/THIRD_PARTY_NOTICES.chromedriver
2025-06-01 21:14:12 - app.services.linkedin_bot - WARNING - initialize_driver:112 - ChromeDriverManager approach failed: [WinError 193] %1 is not a valid Win32 application
2025-06-01 21:14:12 - app.services.linkedin_bot - INFO - initialize_driver:117 - Attempting system Chrome approach...
2025-06-01 21:14:14 - app.services.linkedin_bot - INFO - initialize_driver:120 - System Chrome approach successful
2025-06-01 21:14:14 - app.services.linkedin_bot - INFO - initialize_driver:158 - Testing WebDriver with basic navigation...
2025-06-01 21:14:14 - app.services.linkedin_bot - INFO - initialize_driver:162 - Chrome WebDriver initialized and tested successfully
2025-06-01 21:14:14 - app.services.linkedin_bot - INFO - login:193 - Attempting LinkedIn login <NAME_EMAIL>
2025-06-01 21:14:33 - app.services.linkedin_bot - INFO - login:222 - LinkedIn login successful
2025-06-01 21:14:33 - app.services.linkedin_bot - INFO - search_jobs:276 - Starting job search: Devops Engineer
