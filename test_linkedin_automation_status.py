#!/usr/bin/env python3
"""
Test LinkedIn automation status and identify issues
"""
import sys
import asyncio
sys.path.append('.')

async def test_linkedin_credentials():
    """Test if LinkedIn credentials can be decrypted"""
    try:
        print("🔑 Testing LinkedIn Credentials...")
        
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.core.security import decrypt_credentials
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Test decryption
        try:
            credentials = decrypt_credentials(user.linkedin_email, user.linkedin_password)
            
            if credentials["email"] and credentials["password"]:
                print(f"   ✅ Credentials decrypted successfully!")
                print(f"   📧 LinkedIn email: {credentials['email']}")
                print(f"   🔒 Password length: {len(credentials['password'])} characters")
                return True
            else:
                print("   ❌ Decrypted credentials are empty")
                return False
                
        except Exception as e:
            print(f"   ❌ Decryption failed: {e}")
            return False
        
        finally:
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

async def test_webdriver_initialization():
    """Test WebDriver initialization"""
    try:
        print("\n🔧 Testing WebDriver Initialization...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        
        db = SessionLocal()
        user = db.query(User).first()
        
        if not user:
            print("   ❌ No users found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Test WebDriver initialization
        try:
            await linkedin_bot.initialize_driver()
            
            if linkedin_bot.driver:
                print("   ✅ WebDriver initialized successfully!")
                return True
            else:
                print("   ❌ WebDriver is None after initialization")
                return False
                
        except Exception as e:
            print(f"   ❌ WebDriver initialization failed: {e}")
            return False
        
        finally:
            # Clean up
            if linkedin_bot.driver:
                linkedin_bot.driver.quit()
                print("   ✅ WebDriver closed")
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

async def test_linkedin_login_attempt():
    """Test LinkedIn login attempt (will fail due to 2FA but shows the process)"""
    try:
        print("\n🔗 Testing LinkedIn Login Process...")
        
        from app.services.linkedin_bot import LinkedInBotService
        from app.core.database import SessionLocal
        from app.models.user import User
        
        db = SessionLocal()
        
        # Get user with LinkedIn credentials
        user = db.query(User).filter(User.linkedin_email.isnot(None)).first()
        
        if not user:
            print("   ❌ No users with LinkedIn credentials found")
            return False
            
        print(f"   ✅ Found user: {user.email}")
        
        # Create LinkedIn bot
        linkedin_bot = LinkedInBotService(user, db)
        print("   ✅ LinkedIn bot created")
        
        # Initialize WebDriver
        try:
            await linkedin_bot.initialize_driver()
            print("   ✅ WebDriver initialized")
        except Exception as e:
            print(f"   ❌ WebDriver initialization failed: {e}")
            return False
        
        # Test LinkedIn login (will likely fail due to 2FA)
        try:
            print("   🧪 Attempting LinkedIn login...")
            success = await linkedin_bot.login()
            
            if success:
                print("   ✅ LinkedIn login successful!")
                return True
            else:
                print("   ⚠️  LinkedIn login failed (likely due to 2FA)")
                print("   💡 This is expected if 2FA is enabled on your LinkedIn account")
                return False
                
        except Exception as e:
            print(f"   ❌ LinkedIn login error: {e}")
            return False
        
        finally:
            # Clean up
            if linkedin_bot.driver:
                linkedin_bot.driver.quit()
                print("   ✅ WebDriver closed")
            db.close()
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

def check_database_status():
    """Check database status and job counts"""
    try:
        print("\n💾 Checking Database Status...")
        
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.job import Job
        from app.models.application import Application
        
        db = SessionLocal()
        
        # Check users
        total_users = db.query(User).count()
        users_with_creds = db.query(User).filter(User.linkedin_email.isnot(None)).count()
        
        print(f"   👥 Total users: {total_users}")
        print(f"   🔑 Users with LinkedIn credentials: {users_with_creds}")
        
        # Check jobs
        total_jobs = db.query(Job).count()
        print(f"   💼 Total jobs in database: {total_jobs}")
        
        # Check applications
        total_applications = db.query(Application).count()
        print(f"   📄 Total applications: {total_applications}")
        
        # Show recent jobs
        recent_jobs = db.query(Job).order_by(Job.date_posted.desc()).limit(5).all()
        if recent_jobs:
            print(f"   📋 Recent jobs:")
            for job in recent_jobs:
                print(f"      - {job.title} at {job.company} ({job.date_posted.strftime('%Y-%m-%d')})")
        else:
            print(f"   📋 No jobs found")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database check failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 LinkedIn Automation Status Test")
    print("=" * 60)
    
    # Test database status
    db_ok = check_database_status()
    
    # Test LinkedIn credentials
    creds_ok = await test_linkedin_credentials()
    
    # Test WebDriver
    webdriver_ok = await test_webdriver_initialization()
    
    # Test LinkedIn login (will likely fail due to 2FA)
    login_ok = await test_linkedin_login_attempt()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Database Status: {'✅' if db_ok else '❌'}")
    print(f"   LinkedIn Credentials: {'✅' if creds_ok else '❌'}")
    print(f"   WebDriver Initialization: {'✅' if webdriver_ok else '❌'}")
    print(f"   LinkedIn Login: {'✅' if login_ok else '❌'}")
    
    print("\n🎯 Current Status:")
    if db_ok and creds_ok and webdriver_ok:
        print("   ✅ All core components are working!")
        
        if not login_ok:
            print("   ⚠️  LinkedIn login failed - likely due to 2FA")
            print("\n🔧 To fix the 2FA issue:")
            print("   1. Go to LinkedIn.com in your browser")
            print("   2. Login manually with your credentials")
            print("   3. Go to Settings & Privacy → Sign in & security")
            print("   4. Find 'Two-step verification' and disable it")
            print("   5. Try the job search again in the web app")
            print("\n   OR")
            print("   1. Create a new LinkedIn account without 2FA")
            print("   2. Add the new credentials in app Settings")
            print("   3. Try job search with the new account")
        else:
            print("   🎉 Everything is working! You should be able to search for real LinkedIn jobs!")
    else:
        print("   ❌ Some core components need fixing")
        if not db_ok:
            print("   🔧 Fix database connection issues")
        if not creds_ok:
            print("   🔧 Re-enter LinkedIn credentials in Settings")
        if not webdriver_ok:
            print("   🔧 Check Chrome browser installation")

if __name__ == "__main__":
    asyncio.run(main())
