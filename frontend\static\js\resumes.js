/**
 * Resume management functionality
 */

/**
 * Load resumes data
 */
async function loadResumesData() {
    try {
        showLoading('resumes-view');
        
        // Create resumes view if it doesn't exist
        const resumesView = document.getElementById('resumes-view');
        if (!resumesView.innerHTML.trim()) {
            createResumesView();
        }
        
        await loadResumesList();
        
    } catch (error) {
        console.error('Failed to load resumes data:', error);
        showNotification('Failed to load resumes data', 'error');
    } finally {
        hideLoading('resumes-view');
    }
}

/**
 * Create resumes view HTML
 */
function createResumesView() {
    const resumesView = document.getElementById('resumes-view');
    resumesView.innerHTML = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">Resumes</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <button type="button" class="btn btn-sm btn-primary me-2" onclick="showUploadResumeModal()">
                    <i class="fas fa-upload me-1"></i>Upload Resume
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshResumes()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>

        <!-- Upload Area -->
        <div class="card mb-4" id="upload-area">
            <div class="card-body text-center py-5">
                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                <h5>Upload Your Resume</h5>
                <p class="text-muted">Drag and drop your resume file here, or click to browse</p>
                <input type="file" id="resume-file-input" accept=".pdf,.docx" style="display: none;" onchange="handleFileUpload(event)">
                <button class="btn btn-primary" onclick="document.getElementById('resume-file-input').click()">
                    <i class="fas fa-file-upload me-1"></i>Choose File
                </button>
                <div class="mt-2">
                    <small class="text-muted">Supported formats: PDF, DOCX (Max size: 10MB)</small>
                </div>
            </div>
        </div>

        <!-- Resumes List -->
        <div id="resumes-list">
            <!-- Resumes will be loaded here -->
        </div>

        <!-- Upload Progress Modal -->
        <div class="modal fade" id="uploadProgressModal" tabindex="-1" data-bs-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Uploading Resume</h5>
                    </div>
                    <div class="modal-body">
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <p class="text-center mb-0">Processing your resume...</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Setup drag and drop
    setupDragAndDrop();
}

/**
 * Setup drag and drop functionality
 */
function setupDragAndDrop() {
    const uploadArea = document.getElementById('upload-area');
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('border-primary');
    });
    
    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload({ target: { files: files } });
        }
    });
}

/**
 * Load resumes list
 */
async function loadResumesList() {
    try {
        // For now, we'll create a mock response since the API endpoint isn't implemented
        const resumes = [
            {
                id: 1,
                name: "Software Engineer Resume",
                filename: "resume_2024.pdf",
                file_type: "pdf",
                is_original: true,
                times_used: 5,
                created_at: new Date().toISOString(),
                last_used: new Date().toISOString()
            }
        ];
        
        displayResumesList(resumes);
        
    } catch (error) {
        console.error('Failed to load resumes list:', error);
        showNotification('Failed to load resumes', 'error');
    }
}

/**
 * Display resumes list
 */
function displayResumesList(resumes) {
    const container = document.getElementById('resumes-list');
    
    if (!resumes || resumes.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No resumes uploaded</h4>
                <p class="text-muted">Upload your first resume to get started with job applications.</p>
                <button class="btn btn-primary" onclick="showUploadResumeModal()">
                    <i class="fas fa-upload me-1"></i>Upload Resume
                </button>
            </div>
        `;
        return;
    }
    
    container.innerHTML = `
        <div class="row">
            ${resumes.map(resume => createResumeCard(resume)).join('')}
        </div>
    `;
}

/**
 * Create resume card HTML
 */
function createResumeCard(resume) {
    const fileIcon = resume.file_type === 'pdf' ? 'fa-file-pdf text-danger' : 'fa-file-word text-primary';
    
    return `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100" data-resume-id="${resume.id}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <i class="fas ${fileIcon} fa-2x mb-2"></i>
                            <h5 class="card-title">${resume.name}</h5>
                            <p class="card-text text-muted small">${resume.filename}</p>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="downloadResume(${resume.id})">
                                    <i class="fas fa-download me-2"></i>Download
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="viewResumeVersions(${resume.id})">
                                    <i class="fas fa-history me-2"></i>View Versions
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="customizeResume(${resume.id})">
                                    <i class="fas fa-magic me-2"></i>Customize
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteResume(${resume.id})">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="resume-stats">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-value">${resume.times_used || 0}</div>
                                <div class="stat-label text-muted small">Times Used</div>
                            </div>
                            <div class="col-6">
                                <div class="stat-value">${resume.is_original ? 'Original' : 'Custom'}</div>
                                <div class="stat-label text-muted small">Type</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Uploaded: ${formatRelativeTime(resume.created_at)}
                        </small>
                        ${resume.last_used ? `
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Last used: ${formatRelativeTime(resume.last_used)}
                            </small>
                        ` : ''}
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="useResumeForApplication(${resume.id})">
                            <i class="fas fa-paper-plane me-1"></i>Use for Application
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Handle file upload
 */
async function handleFileUpload(event) {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Validate file
    if (!validateFile(file)) return;
    
    try {
        // Show progress modal
        const progressModal = new bootstrap.Modal(document.getElementById('uploadProgressModal'));
        progressModal.show();
        
        // Simulate upload progress
        const progressBar = document.querySelector('#uploadProgressModal .progress-bar');
        let progress = 0;
        
        const progressInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);
        
        // Create form data
        const formData = new FormData();
        formData.append('file', file);
        
        // Upload file (mock for now)
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Complete progress
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        
        setTimeout(() => {
            progressModal.hide();
            showNotification('Resume uploaded successfully!', 'success');
            loadResumesList();
        }, 500);
        
    } catch (error) {
        console.error('Upload failed:', error);
        showNotification('Failed to upload resume: ' + error.message, 'error');
        
        // Hide progress modal
        const progressModal = bootstrap.Modal.getInstance(document.getElementById('uploadProgressModal'));
        if (progressModal) progressModal.hide();
    }
    
    // Clear file input
    event.target.value = '';
}

/**
 * Validate file
 */
function validateFile(file) {
    // Check file type
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
        showNotification('Please upload a PDF or DOCX file', 'error');
        return false;
    }
    
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        showNotification('File size must be less than 10MB', 'error');
        return false;
    }
    
    return true;
}

/**
 * Show upload resume modal
 */
function showUploadResumeModal() {
    document.getElementById('resume-file-input').click();
}

/**
 * Download resume
 */
async function downloadResume(resumeId) {
    showNotification('Download feature coming soon!', 'info');
}

/**
 * View resume versions
 */
async function viewResumeVersions(resumeId) {
    showNotification('Version history feature coming soon!', 'info');
}

/**
 * Customize resume
 */
async function customizeResume(resumeId) {
    showNotification('Resume customization feature coming soon!', 'info');
}

/**
 * Delete resume
 */
async function deleteResume(resumeId) {
    if (!confirm('Are you sure you want to delete this resume? This action cannot be undone.')) {
        return;
    }
    
    try {
        // Mock delete operation
        showNotification('Resume deleted successfully', 'success');
        await loadResumesList();
        
    } catch (error) {
        showNotification('Failed to delete resume', 'error');
    }
}

/**
 * Use resume for application
 */
async function useResumeForApplication(resumeId) {
    showNotification('Application feature coming soon!', 'info');
}

/**
 * Refresh resumes
 */
async function refreshResumes() {
    const refreshButton = document.querySelector('[onclick="refreshResumes()"]');
    const originalContent = refreshButton.innerHTML;
    
    try {
        refreshButton.innerHTML = '<i class="fas fa-sync-alt fa-spin me-1"></i>Refreshing...';
        refreshButton.disabled = true;
        
        await loadResumesList();
        showNotification('Resumes refreshed successfully', 'success');
        
    } catch (error) {
        showNotification('Failed to refresh resumes', 'error');
    } finally {
        refreshButton.innerHTML = originalContent;
        refreshButton.disabled = false;
    }
}
