                        submit_button.click()
                        logger.info("Application submitted successfully")
                        time.sleep(random.uniform(2, 3))
                        return True
                    except TimeoutException:
                        # Check if we need to fill in any required fields
                        required_fields = self._fill_required_fields()
                        if not required_fields:
                            logger.info("No more steps found and no submit button. Application may be incomplete.")
                            return False
                        
        except Exception as e:
            logger.error(f"Error applying to job: {str(e)}")
            return False
    
    def _fill_required_fields(self) -> bool:
        """
        Fill in required fields in the application form.
        
        Returns:
            True if fields were filled, False otherwise
        """
        try:
            # Look for input fields, textareas, and select elements
            input_fields = self.driver.find_elements(By.CSS_SELECTOR, "input:not([type='hidden']):not([type='file'])")
            textareas = self.driver.find_elements(By.CSS_SELECTOR, "textarea")
            selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
            
            filled_something = False
            
            # Fill input fields
            for field in input_fields:
                if field.is_displayed() and field.get_attribute("value") == "":
                    field_id = field.get_attribute("id") or ""
                    field_name = field.get_attribute("name") or ""
                    field_type = field.get_attribute("type") or ""
                    
                    # Skip certain types of fields
                    if field_type in ["button", "submit", "reset", "checkbox", "radio"]:
                        continue
                    
                    # Try to get value from config based on field id or name
                    value = None
                    if field_id and self.config.has_option('ApplicationAnswers', field_id):
                        value = self.config.get('ApplicationAnswers', field_id)
                    elif field_name and self.config.has_option('ApplicationAnswers', field_name):
                        value = self.config.get('ApplicationAnswers', field_name)
                    elif "phone" in field_id.lower() or "phone" in field_name.lower():
                        value = self.config.get('ApplicationAnswers', 'phone', fallback='')
                    
                    if value:
                        field.clear()
                        field.send_keys(value)
                        filled_something = True
                        logger.info(f"Filled field {field_id or field_name} with value from config")
                        time.sleep(random.uniform(0.5, 1))
            
            # Fill textareas (often used for cover letters or additional information)
            for textarea in textareas:
                if textarea.is_displayed() and textarea.get_attribute("value") == "":
                    textarea_id = textarea.get_attribute("id") or ""
                    textarea_name = textarea.get_attribute("name") or ""
                    
                    # Try to get value from config
                    value = None
                    if textarea_id and self.config.has_option('ApplicationAnswers', textarea_id):
                        value = self.config.get('ApplicationAnswers', textarea_id)
                    elif textarea_name and self.config.has_option('ApplicationAnswers', textarea_name):
                        value = self.config.get('ApplicationAnswers', textarea_name)
                    elif "cover" in textarea_id.lower() or "cover" in textarea_name.lower():
                        value = self.config.get('ApplicationAnswers', 'cover_letter', fallback='')
                    
                    if value:
                        textarea.clear()
                        textarea.send_keys(value)
                        filled_something = True
                        logger.info(f"Filled textarea {textarea_id or textarea_name} with value from config")
                        time.sleep(random.uniform(0.5, 1))
            
            # Handle select elements (dropdowns)
            for select in selects:
                if select.is_displayed():
                    select_id = select.get_attribute("id") or ""
                    select_name = select.get_attribute("name") or ""
                    
                    # Try to get value from config
                    value = None
                    if select_id and self.config.has_option('ApplicationAnswers', select_id):
                        value = self.config.get('ApplicationAnswers', select_id)
                    elif select_name and self.config.has_option('ApplicationAnswers', select_name):
                        value = self.config.get('ApplicationAnswers', select_name)
                    
                    if value:
                        # Find and click the option with matching text
                        try:
                            option = select.find_element(By.XPATH, f".//option[contains(text(), '{value}')]")
                            option.click()
                            filled_something = True
                            logger.info(f"Selected option '{value}' for dropdown {select_id or select_name}")
                            time.sleep(random.uniform(0.5, 1))
                        except NoSuchElementException:
                            logger.warning(f"Could not find option '{value}' in dropdown {select_id or select_name}")
            
            return filled_something
            
        except Exception as e:
            logger.error(f"Error filling required fields: {str(e)}")
            return False
    
    def save_jobs_to_csv(self, jobs: List[Dict[str, str]], filename: str = "linkedin_jobs.csv") -> None:
        """
        Save the list of jobs to a CSV file.
        
        Args:
            jobs: List of job dictionaries
            filename: Name of the CSV file to create
        """
        if not jobs:
            logger.info("No jobs to save")
            return
            
        try:
            import csv
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['id', 'title', 'company', 'location', 'date_posted', 'url']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for job in jobs:
                    # Create a new dict with only the fields we want
                    job_data = {field: job.get(field, '') for field in fieldnames}
                    writer.writerow(job_data)
                    
            logger.info(f"Saved {len(jobs)} jobs to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving jobs to CSV: {str(e)}")
    
    def close(self) -> None:
        """Close the browser and clean up resources."""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")


def main():
    """Main function to run the LinkedIn Job Application Bot."""
    parser = argparse.ArgumentParser(description='LinkedIn Job Application Bot')
    parser.add_argument('--config', type=str, default='config.ini',
                        help='Path to configuration file (default: config.ini)')
    parser.add_argument('--save-csv', action='store_true',
                        help='Save job listings to CSV file')
    args = parser.parse_args()
    
    try:
        # Initialize the bot
        bot = LinkedInBot(args.config)
        
        # Login to LinkedIn
        if not bot.login():
            logger.error("Failed to log in. Exiting.")
            bot.close()
            return
        
        # Search for jobs
        jobs = bot.search_jobs()
        
        # Display jobs and get user selection
        bot.display_jobs(jobs)
        
        # Save to CSV if requested
        if args.save_csv:
            bot.save_jobs_to_csv(jobs)
        
        # Get user selection
        selected_indices = bot.get_user_selection(jobs)
        
        if not selected_indices:
            logger.info("No jobs selected for application. Exiting.")
            bot.close()
            return
        
        # Process selected jobs
        for idx in selected_indices:
            job = jobs[idx]
            logger.info(f"Processing job: {job['title']} at {job['company']}")
            
            # Analyze job description
            job_details = bot.analyze_job_description(job['url'])
            
            # Customize resume
            custom_resume_path = bot.customize_resume(job_details)
            
            # Apply to job
            success = bot.apply_to_job(job_details, custom_resume_path)
            
            if success:
                logger.info(f"Successfully applied to: {job['title']} at {job['company']}")
            else:
                logger.info(f"Could not apply automatically to: {job['title']} at {job['company']}")
            
            # Add a delay between applications to avoid being flagged
            time.sleep(random.uniform(5, 10))
        
        logger.info("Job application process completed")
        
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
    finally:
        if 'bot' in locals():
            bot.close()


if __name__ == "__main__":
    main()